{% if page_obj.has_other_pages %}
    <div class="ts-pagination text-center mb-20">
        <ul class="pagination">
            {% if page_obj.has_previous %}
                <li><a href="?page={{ page_obj.previous_page_number }}"><i class="fa fa-angle-left"></i></a></li>
            {% endif %}
            
            {% for num in page_obj.paginator.page_range %}
                {% if page_obj.number == num %}
                    <li class="active"><a href="#">{{ num }}</a></li>
                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li><a href="?page={{ num }}">{{ num }}</a></li>
                {% endif %}
            {% endfor %}
            
            {% if page_obj.has_next %}
                <li><a href="?page={{ page_obj.next_page_number }}"><i class="fa fa-angle-right"></i></a></li>
            {% endif %}
        </ul>
    </div>
{% endif %} 
"""
StreamField configurations for college models
"""
from wagtail.fields import <PERSON><PERSON>ield

from college.blocks.base_blocks import (
    ImageBlock,
    VideoBlock,
    HtmlBlock,
    GalleryBlock,
    CalloutBlock,
    RichTextEditorBlock,
    EnhancedTableBlock
)


def get_college_content_blocks():
    """
    Returns the default content StreamField blocks for college posts
    """
    return [
        ('rich_text', RichTextEditorBlock(help_text="Full-featured rich text editor with advanced formatting")),
        ('image', ImageBlock(help_text="Single image with caption")),
        ('video', VideoBlock(help_text="Embed YouTube or Vimeo videos")),
        ('html', HtmlBlock(help_text="Custom HTML code")),
        ('gallery', GalleryBlock(help_text="Multiple images displayed as a gallery")),
        ('callout', CalloutBlock(help_text="Highlighted text callout")),
        ('table', EnhancedTableBlock(help_text="Table with caption and header row options"))
    ]


def get_college_content_field():
    """
    Returns a configured StreamField for college content
    """
    return StreamField(
        get_college_content_blocks(),
        use_json_field=True,
        blank=True,
        null=True,
        verbose_name="Article Content"
    )

from wagtail import blocks


class RichTextEditorBlock(blocks.RichTextBlock):
    """
    Full-featured rich text editor block with advanced formatting capabilities
    """
    def __init__(self, **kwargs):
        features = [
            'h2', 'h3', 'h4', 'h5', 'h6',
            'bold', 'italic', 'ol', 'ul', 'hr',
            'link', 'document-link', 'embed',
            'code', 'superscript', 'subscript', 'strikethrough',
            'blockquote', 'table', 'image'
        ]
        
        kwargs.setdefault('features', features)
        super().__init__(**kwargs)
    
    class Meta:
        icon = 'pilcrow'
        template = 'blocks/rich_text_editor_block.html'
        label = 'Text Editor'
# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Ignore all Django migration files except __init__.py
**/migrations/*.py
!**/migrations/__init__.py
**/migrations/*.pyc

# Virtual Environment
venv/
ENV/
.env
.venv

# Django/Wagtail
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
public/static-collected/

# Migrations - keep folders and __init__.py files only
*/migrations/*
!*/migrations/
!*/migrations/__init__.py


# Wagtail specific
*/media/images/*
*/media/original_images/*
*/static/CACHE/*

# Front-end
node_modules/
npm-debug.log
.DS_Store
*.map

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
*~
.project
.pydevproject
.settings/

# Testing
.coverage
htmlcov/
.tox/
.pytest_cache/

# Docker
.docker/

# Deployment
.elasticbeanstalk/
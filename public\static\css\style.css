/*
	Template Name: Vinazine
	Author: Themewinter
	Author URI: https://themeforest.net/user/tripples
	Description: vinazine
  Version: 1.0

=====================
   table of content 
====================

1.   Typography
2.   Global Styles
3.   Top Bar
4.   header middle area
5.   Header area
6.   Slider
7.   Content area
8.   blog section
9.   Footer
10.  Sub Pages
11.  News Single
12.  Sidebar
13.  Error page

*/
/*------- social color -----*/
@import url("https://fonts.googleapis.com/css?family=Arimo:400,400i,700,700i");
@import url("https://fonts.googleapis.com/css?family=Heebo:400,500,700,800,900");
@import url("https://fonts.googleapis.com/css?family=Merriweather:400,400i,700,700i,900,900i");
/* ============================== */
/* Typography
================================================== */
body {
  color: #8a8a8a;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
  font-size: 14px;
  line-height: 24px;
  font-family: "Arimo", sans-serif;
}

.body-color {
  background: #f0f1f4;
}

h1,
h2,
h3,
h4,
.nav-menu li a,
.post-cat,
.post-list-item .nav-tabs li a,
.ts-social-list li b,
.widgets.ts-social-list-item ul li a b,
.footer-social li a,
.ts-cat-title span {
  font-family: "Heebo", sans-serif;
}

h1 {
  font-size: 36px;
  font-weight: 700;
  color: #222222;
}

h2 {
  font-size: 28px;
  font-weight: 700;
  color: #222222;
}

h3 {
  font-size: 20px;
  font-weight: 700;
  color: #222222;
}

h4 {
  font-size: 16px;
  font-weight: 700;
}

p {
  margin-bottom: 1rem;
  font-size: 15px;
  color: #232323;
}

/* Global styles
================================================== */
a,
a:active,
a:focus,
a:hover,
a:visited {
  text-decoration: none;
  outline: 0 solid;
}

a {
  -webkit-transition: all ease 500ms;
  -o-transition: all ease 500ms;
  transition: all ease 500ms;
}

a:hover {
  color: #d72924;
}

.btn {
  font-size: 13px;
  text-transform: uppercase;
  color: #fff;
  cursor: pointer;
  outline: none;
  border: none;
  padding: 13px 37px;
  border-radius: 0;
  font-weight: 600;
}

.btn.btn-primary {
  background: #e91e63;
}

.btn:hover {
  background: #222;
}

button:focus,
.btn:focus {
  outline: none;
  -webkit-box-shadow: none;
  box-shadow: none !important;
}

ul {
  padding: 0;
  margin: 0;
  list-style: none;
}

section, .section {
  padding: 15px 0;
}

.section-bg {
  background: #f7f7f7;
}

.pl-0 {
  padding-left: 0;
}

.pr-0 {
  padding-right: 0;
}

.pb-0 {
  padding-bottom: 0;
}

.pt-0 {
  padding-top: 0;
}

.p-0 {
  padding: 0;
}

.pr-10 {
  padding-right: 10px;
}

.p-30 {
  padding: 30px 0;
}

.p-50 {
  padding: 50px 0;
}

.p-60 {
  padding: 60px 0;
}

.pt-110 {
  padding-top: 110px;
}

.p-10 {
  padding: 0 10px;
}

.plr-30 {
  padding: 0 30px;
}

.plr-1 {
  padding: 0 5px;
}

.plr-10 {
  padding: 0 10px;
}

.p-100 {
  padding: 100px 0;
}

.mt-15 {
  margin-top: 15px;
}

.mt-50 {
  margin-top: 50px;
}

.mb-10 {
  margin-bottom: 10px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mb-15 {
  margin-bottom: 15px;
}

.mb-25 {
  margin-bottom: 25px;
}

.mb-30 {
  margin-bottom: 30px !important;
}

.mb-35 {
  margin-bottom: 35px;
}

.mb-40 {
  margin-bottom: 40px;
}

.mb-45 {
  margin-bottom: 45px;
}

.mb-50 {
  margin-bottom: 50px;
}

.mb-60 {
  margin-bottom: 60px;
}

.mr--20 {
  margin-right: -20px;
}

.post-cat {
  background: #007dff;
}

/*Text color --*/
.green-color {
  color: #4ab106;
}

.blue-color {
  color: #007dff;
}

.blue-dark-color {
  color: #005689;
}

.bl-light-color {
  color: #007dff;
}

.pink-color {
  color: #ff5575;
}

.yellow-color {
  color: #ffaf31;
}

.purple-color {
  color: #6200ee !important;
}

/*---- Background color --*/
.ts-green-bg {
  background: #4ab106;
}

.ts-blue-bg {
  background: #007dff;
}

.ts-blue-dark-bg {
  background: #005689;
}

.ts-blue-light-bg {
  background: #007dff;
}

.ts-pink-bg {
  background: #ff5575;
}

.ts-yellow-bg {
  background: #ffaf31;
}

.ts-orange-bg {
  background: #ff6e0d;
}

.ts-purple-bg {
  background: #6200ee;
}

.ts-white-bg {
  background: #fff;
}

.ts-blue-light-heighlight {
  background: #007dff;
}

/* social color */
.ts-facebook a i {
  background: #3b5999;
}

.ts-google-plus a i {
  background: #dd4b39;
}

.ts-twitter a i {
  background: #55acee;
}

.ts-pinterest a i {
  background: #bd081c;
}

.ts-linkedin a i {
  background: #007bb6;
}

.ts-youtube a i {
  background: #f33220;
}

/* defailt title setting */
.ts-title {
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 25px;
  position: relative;
}

.ts-title:before {
  position: absolute;
  left: -30px;
  top: 0;
  width: 3px;
  height: 100%;
  content: '';
  background: #d72924;
}

.ts-title.white {
  color: #fff;
}

.post-title {
  font-size: 15px;
  font-weight: 500;
  color: #232323;
  margin-bottom: 5px;
}

.post-title.lg {
  font-size: 36px;
  margin-bottom: 17px;
}

.post-title.ex-lg {
  font-weight: 700;
  font-size: 44px;
  color: #212121;
  margin-bottom: 15px;
}

.post-title.md {
  font-size: 20px;
  line-height: 24px;
  margin-bottom: 10px;
}

.post-title.md-title {
  font-size: 24px;
  margin-bottom: 10px;
}

.post-title.large {
  font-size: 30px;
  margin-bottom: 15px;
  font-weight: 500;
}

.post-title a {
  color: #222222;
}

.post-title a:hover {
  color: #d72924;
}

.border-top {
  border-top: 1px solid #e9e9e9;
}

.border-top.bar1 {
  border-color: #4d4d70;
}

#featured-slider .owl-nav {
  position: absolute;
  right: 0;
  top: 0;
}

#featured-slider .owl-nav .owl-prev:before {
  position: absolute;
  right: 0;
  top: 0px;
  bottom: 0;
  width: 1px;
  height: 15px;
  background: #406a91;
  content: '';
  margin: auto;
}

#featured-slider .owl-nav .owl-prev,
#featured-slider .owl-nav .owl-next {
  font-size: 14px;
  background: #00386c;
  color: #fff;
  width: 28px;
  height: 28px;
  display: inline-block;
  position: relative;
  margin: 0;
}

/* post meta --*/
.post-meta-info {
  margin-bottom: 10px;
  padding-left: 0;
}

.post-meta-info li {
  font-size: 13px;
  display: inline-block;
  position: relative;
  margin-right: 15px;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
}

.post-meta-info li i {
  margin-right: 6px;
  font-size: 15px;
}

.post-meta-info li a {
  color: #a9a9a9;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
}

.post-meta-info li a:hover {
  color: #d72924;
}

.post-meta-info li a.post-cat {
  margin-left: 0;
  color: #fff;
}

.post-meta-info li.author {
  padding-left: 40px;
}

.post-meta-info li.author img {
  position: absolute;
  left: 0;
  top: 0;
  width: 25px;
  height: 25px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -ms-border-radius: 50%;
}

.post-meta-info li:last-child {
  margin-right: 0;
}

.post-meta-info li.active {
  color: #d72924;
}

/*- blocqoute */
blockquote {
  font-size: 24px;
  font-weight: 700;
  font-style: italic;
  color: #232323;
  line-height: 36px;
  padding: 0 0 0 75px;
  position: relative;
  margin: 50px 50px;
  z-index: 1;
}

blockquote:before {
  content: '\f10d';
  font-family: 'Fontawesome';
  position: absolute;
  left: 0;
  font-size: 96px;
  background: #f0f1f4;
  display: block;
  /* width: 100px; */
  height: 125%;
  color: #fff;
  line-height: 102px;
  padding: 0 37px 0 0;
  font-style: normal;
  z-index: -1;
  top: 0;
  bottom: 0;
  margin: auto;
}

blockquote cite {
  font-weight: 700;
  float: right;
  position: relative;
  font-size: 16px;
}

blockquote cite:after {
  position: absolute;
  right: -50px;
  top: 0;
  width: 40px;
  height: 2px;
  background: #d72924;
  content: '';
  bottom: 0;
  margin: auto;
}

/*------------------ owl carosel ---*/
.owl-nav {
  position: absolute;
  right: 0;
  top: -50px;
}

.owl-nav .owl-next,
.owl-nav .owl-prev {
  margin: 0 0 0 20px;
  display: inline-block;
  position: relative;
}

.owl-nav .owl-next i,
.owl-nav .owl-prev i {
  font-size: 20px;
  line-height: 27px;
}

.owl-nav .owl-next:hover i,
.owl-nav .owl-prev:hover i {
  color: #d72924;
}

.owl-nav .owl-prev:before {
  position: absolute;
  right: -11px;
  top: 0px;
  width: 1px;
  height: 15px;
  background: #ddd;
  content: '';
  bottom: 0;
  margin: auto;
  display: block;
  text-align: center;
}

.owl-dots {
  position: absolute;
  right: 0;
  top: -49px;
  z-index: 1;
}

.owl-dots .owl-dot span {
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background: #d72924;
  display: inline-block;
  opacity: 1;
  margin: 0 3px;
  opacity: .5;
}

.owl-dots .owl-dot.active span {
  height: 10px;
  width: 10px;
  border-radius: 50%;
  position: relative;
  top: 2px;
  opacity: 1;
}

#more-news-slider .owl-dots .owl-dot span {
  background: #fff;
}

/*------- social color -----*/
/*  */
/*-------------------------
  preloader 
  ---------------------*/
#preloader {
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: 99999999999;
  overflow: hidden;
  background-color: #d72924;
  -webkit-transition: all 1.5s ease-out;
  -o-transition: all 1.5s ease-out;
  transition: all 1.5s ease-out;
}

#preloader.loaded {
  top: -200%;
}

#preloader.loaded .preloader-cancel-btn-wraper {
  bottom: 200%;
}

.preloader-cancel-btn-wraper {
  position: fixed;
  bottom: 0;
  right: 0;
  padding: 30px;
  -webkit-transition: all 1.5s ease-out;
  -o-transition: all 1.5s ease-out;
  transition: all 1.5s ease-out;
}

.preloader-cancel-btn-wraper .preloader-cancel-btn {
  border-radius: 36px;
  font-size: 11px;
  padding: 13px 23px;
  background: #9a2521;
}

.spinner {
  width: 40px;
  height: 40px;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.double-bounce1 {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: #FFFFFF;
  opacity: 0.6;
  position: absolute;
  top: 0;
  left: 0;
  -webkit-animation: sk-bounce 2.0s infinite ease-in-out;
  animation: sk-bounce 2.0s infinite ease-in-out;
}

.double-bounce2 {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: #FFFFFF;
  opacity: 0.6;
  position: absolute;
  top: 0;
  left: 0;
  -webkit-animation: sk-bounce 2.0s infinite ease-in-out;
  animation: sk-bounce 2.0s infinite ease-in-out;
  -webkit-animation-delay: -1.0s;
  animation-delay: -1.0s;
}

@-webkit-keyframes sk-bounce {
  0%, 100% {
    transform: scale(0);
    -webkit-transform: scale(0);
  }
  50% {
    transform: scale(1);
    -webkit-transform: scale(1);
  }
}

@keyframes sk-bounce {
  0%, 100% {
    transform: scale(0);
    -webkit-transform: scale(0);
  }
  50% {
    transform: scale(1);
    -webkit-transform: scale(1);
  }
}

/*------------------------------
  top bar
------------------------------*/
.ts-top-nav {
  padding-left: 26px;
}

.ts-top-nav li {
  display: inline-block;
  margin-right: 16px;
}

.ts-top-nav li a {
  font-size: 13px;
  color: #8a8a8a;
  line-height: 40px;
}

.ts-top-nav li a:hover {
  color: #d72924;
}

.ts-top-nav li:last-of-type {
  margin-right: 0;
}

.top-social li {
  display: inline-block;
  line-height: 41px;
}

.top-social li a {
  color: #8a8a8a;
  font-size: 14px;
  padding: 0 7px;
}

.top-social li.ts-date {
  padding: 0 14px;
  margin-left: 19px;
  background: #ffffff;
  color: #8a8a8a;
  font-weight: 600;
}

.top-social li.ts-subscribe {
  padding: 0 30px;
  margin-left: 19px;
  background: #d72924;
  color: #fff;
  text-transform: uppercase;
  font-weight: 700;
}

.top-social li.ts-subscribe a {
  color: #fff;
}

.top-bar {
  background: #fff;
  padding: 0;
  /*index 2----*/
  /*---- version  ---*/
}

.top-bar .ts-temperature {
  display: inline-block;
  padding-right: 30px;
  position: relative;
}

.top-bar .ts-temperature:before {
  position: absolute;
  right: 0;
  top: 3px;
  width: 1px;
  height: 20px;
  content: '';
  background: #e9e9e9;
}

.top-bar .ts-temperature i {
  color: #ffaf31;
}

.top-bar .ts-temperature span b {
  font-weight: 400;
  position: relative;
  top: -4px;
}

.top-bar .ts-top-nav {
  display: inline-block;
}

.top-bar.v2 {
  background: #c2211c;
}

.top-bar.v2 .ts-breaking-news {
  margin-bottom: 0;
  background: transparent;
  padding: 0;
}

.top-bar.v2 .ts-breaking-news .breaking-title {
  color: #fff;
}

.top-bar.v2 .ts-breaking-news p a {
  color: #fff;
}

.top-bar.v2 .top-social li a {
  color: #fff;
}

.top-bar.v3 .ts-date {
  display: inline-block;
  padding-right: 50px;
  font-weight: 500;
  font-size: 14px;
  color: #8a8a8a;
}

.top-bar.v3 .ts-temperature {
  border-right: none;
  padding-right: 0;
}

.top-bar .ts-date {
  font-size: 13px;
  font-weight: 500;
  color: #606060;
}

.top-bar .ts-date-item {
  background: #d72924;
  display: inline-block;
  line-height: 42px;
  padding: 0 14px;
  color: #fff;
}

.top-bar.top-bg {
  background: #f4f4f4;
}

.top-bar .top-nav li {
  display: inline-block;
}

.top-bar .top-nav li a {
  font-size: 13px;
  color: #606060;
  line-height: 26px;
  line-height: 34px;
  margin-left: 35px;
}

.top-bar.bg-blue-dark {
  background: #004e7c;
}

.top-bar.bg-blue-dark .ts-date {
  color: #fff;
}

.top-bar.bg-blue-dark .ts-top-nav li a {
  color: #dde7ee;
}

.top-bar.v4 {
  background: #2c2c2c;
}

.top-bar.v4 .ts-breaking-news {
  margin-bottom: 0;
  background: transparent;
  padding: 8px 20px 8px 0;
}

.top-bar.v4 .ts-breaking-news .breaking-post-content p a {
  color: #fff;
}

.top-bar.v5 .ts-breaking-news {
  padding: 7px 20px 7px 0;
  margin-bottom: 0;
}

/*=============================
         header middle
 =============================*/
.header-middle {
  padding: 20px 0;
}

.header-middle .banner-img {
  text-align: center;
}

.header-middle.v2 {
  background: #d72924;
  padding: 4px 0;
  margin-bottom: 5px;
}

.header-middle.v2 .logo a {
  text-align: left;
}

.header-middle.v3 {
  padding: 25px 0;
}

.header-middle.v4 {
  padding: 38px 0;
}

.banner-img img {
  max-width: 100%;
}

.bg-blue {
  background: #005689;
}

.currency-list-item ul li {
  display: inline-block;
  border-right: 1px solid #1a6795;
  padding-right: 10px;
}

.currency-list-item ul li .currency-item {
  display: inline-block;
  padding: 0 14px;
}

.currency-list-item ul li .currency-item label {
  display: block;
  font-size: 14px;
  font-weight: 700;
  color: #fff;
  letter-spacing: 0.7px;
  text-transform: uppercase;
}

.currency-list-item ul li .currency-item span {
  font-size: 12px;
}

.currency-list-item ul li .currency-item.right {
  text-align: right;
}

.currency-list-item ul li:nth-child(odd) .currency-item span {
  color: #ff433d;
}

.currency-list-item ul li:nth-child(even) .currency-item span {
  color: #11e260;
}

.currency-list-item ul li:last-child {
  padding-right: 0;
  border-right: none;
}

.currency-list-item ul li:last-child .currency-item.right {
  padding-right: 0;
}

/*=============================
        header nav
 =============================*/
@media (min-width: 992px) {
  .ts-menu-sticky.sticky {
    -webkit-box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.08);
    box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.08);
    position: fixed;
    top: 0;
    z-index: 9;
    width: 100%;
    left: 0;
    right: 0;
    margin: auto;
    max-width: 930px;
  }
  .ts-menu-sticky.sticky.fade_down_effect {
    -webkit-animation-name: fadeInDown;
    animation-name: fadeInDown;
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-delay: .1s;
    animation-delay: .1s;
  }
}

@media (min-width: 1200px) {
  .ts-menu-sticky.sticky {
    max-width: 1110px;
  }
}

/*------ nav top ------ */
.header-nav-item {
  padding-left: 5px;
}

.breaking-title {
  font-size: 13px;
  text-transform: uppercase;
  color: #d72924;
  font-weight: 700;
  margin-bottom: 0;
  line-height: 26px;
  padding-right: 10px;
}

.breaking-title i {
  font-size: 15px;
  margin-right: 6px;
}

.mobile-logo {
  display: none;
}

.ts-breaking-news {
  background: #fff;
  padding: 12px 20px;
  margin-bottom: 5px;
}

.ts-breaking-news p {
  margin-bottom: 0;
}

.ts-breaking-news p a {
  font-size: 13px;
  line-height: 26px;
  color: #5c5c5c;
}

.ts-breaking-news .breaking-news-content {
  width: 80%;
}

#breaking_slider .owl-nav {
  position: absolute;
  right: -49px;
  top: 1px;
}

#breaking_slider .owl-nav .owl-prev,
#breaking_slider .owl-nav .owl-next {
  width: 24px;
  background: #f0f1f4;
  color: #222;
  line-height: 19px;
  margin-left: 5px;
  height: 24px;
  font-size: 16px;
}

#breaking_slider .owl-nav .owl-prev:before,
#breaking_slider .owl-nav .owl-next:before {
  display: none;
}

#breaking_slider .owl-nav .owl-prev:hover,
#breaking_slider .owl-nav .owl-next:hover {
  background: #d72924;
  color: #fff;
}

.header-default {
  position: relative;
}

.logo {
  background: #ffffff;
  height: 110px;
}

.logo a {
  display: block;
  text-align: center;
  line-height: 115px;
}

.nav-header {
  padding: 0;
  margin: 0;
  width: 0;
}

.navigation {
  height: 60px;
  display: block;
}

.nav-menu > li > a {
  height: 60px;
  padding: 22px 20px;
  text-transform: uppercase;
  font-weight: 700;
  color: #232323;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
}

.nav-menu > li > a .submenu-indicator {
  -webkit-transition: all 0s;
  -o-transition: all 0s;
  transition: all 0s;
  margin-top: 0px;
}

.nav-menu > li > a .submenu-indicator:before {
  content: '\f0d7';
  font-size: 14px;
  font-family: 'FontAwesome';
}

.nav-menu > li > a .submenu-indicator .submenu-indicator-chevron {
  display: none;
}

.nav-menu > li > a:hover {
  background: #d72924;
  color: #fff !important;
}

.nav-menu > li .nav-dropdown li a {
  font-weight: 700;
  height: 50px;
  padding: 16px 20px;
}

.nav-menu > li.focus > a {
  color: #d72924;
}

.nav-menu > li.active > a {
  background: #004c1d !important;
  color: #fff;
}

.nav-menu > li.active > a .submenu-indicator-chevron {
  border-color: transparent #ffffff #ffffff transparent;
}

.nav-menu > li .nav-dropdown {
  -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
}

.nav-menu > li .nav-dropdown li a {
  font-size: 14px;
  color: #232323;
  border: none;
  padding: 14px 16px;
  font-weight: 400;
}

.nav-menu > li .nav-dropdown li a:hover {
  color: #d72924;
}

.nav-menu > li .nav-dropdown li a:before {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 85%;
  right: 0;
  margin: auto;
  height: 1px;
  background: #eae8e8;
  content: '';
}

.nav-menu > li .nav-dropdown li:last-child a:before {
  display: none;
}

.nav-menu .megamenu-panel {
  -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
}

.nav-menu .megamenu-lists .megamenu-list > li > a {
  font-size: 14px;
  color: #232323;
}

.nav-menu .megamenu-lists .megamenu-list > li > a:hover {
  color: #d72924;
  background: transparent;
}

.nav-menu .megamenu-tabs-nav {
  background: #f0f1f4;
}

.nav-menu .megamenu-tabs-nav > li > a {
  font-size: 14px;
  color: #232323;
  border: none;
  padding: 14px 16px 14px 30px;
}

.nav-menu .megamenu-tabs-nav > li > a:hover {
  color: #d72924;
  background: #f0f1f4;
}

.nav-menu .megamenu-tabs-nav > li.active a {
  background: transparent;
  color: #d72924;
}

.nav-menu .megamenu-tabs {
  padding: 15px 0;
}

.nav-menu .megamenu-tabs-pane {
  border: none;
  border-left: 1px solid #f0f1f4;
  padding-left: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.right-menu li {
  float: left;
}

.right-menu li a {
  line-height: 61px;
  width: 60px;
  height: 60px;
  font-size: 19px;
  display: block;
  text-align: center;
  border-left: 1px solid #e9e9e9;
  color: #8a8a8a;
}

.right-menu li .nav-search {
  height: 60px;
}

.right-menu li .nav-search .nav-search-button {
  height: 100%;
  line-height: 64px;
  color: #fff;
  font-size: 17px;
  width: 60px;
  background: #004c1d;
}

.right-menu .nav-search-inner input[type=text], .right-menu .nav-search-inner input[type=search] {
  height: 60px;
  line-height: 60px;
  font-size: 20px;
}

.right-menu .nav-search-close-button {
  top: 16px;
}

/*----------- header-standerd---------*/
.header-standerd {
  background: #d72924;
}

.header-standerd .navigation {
  background: transparent;
}

.header-standerd .nav-menu > li > a {
  color: #fff;
}

.header-standerd .nav-menu > li > a:hover {
  background: #c2211c;
}

.header-standerd .nav-menu > li.active > a {
  background: #c2211c;
}

.header-standerd .right-menu li a {
  border-left: none;
  color: #fff;
  position: relative;
}

.header-standerd .right-menu li a:before {
  position: absolute;
  right: 0;
  top: 0;
  width: 1px;
  height: 20px;
  content: '';
  bottom: 0;
  margin: auto;
  background: #fff;
}

/*-------  vertion 3 ------*/
.menu-centerd .ts-main-menu {
  text-align: center !important;
}

.nav-menu-item {
  position: relative;
}

.nav-menu-item .ts-main-menu .nav-menu > li > a {
  font-size: 16px;
  height: 70px;
  font-weight: 400;
  color: #212121;
  padding: 22px 28px;
  text-transform: capitalize;
}

.nav-menu-item .ts-main-menu .nav-menu > li > a:hover {
  background: #fff;
}

.nav-menu-item .ts-main-menu .nav-menu > li .nav-dropdown li a:hover {
  background: #6cba40;
  color: #fff;
}

.nav-menu-item .ts-main-menu .nav-menu li.active > a {
  background: transparent;
  color: #6cba40;
}

.nav-menu-item .ts-main-menu .right-menu li .nav-search .nav-search-button {
  background: transparent;
  color: #212121;
  width: 50px;
  height: auto;
  line-height: 69px;
  font-size: 16px;
}

.nav-menu-item.nav-icon-item .navigation {
  height: 114px;
}

.nav-menu-item.nav-icon-item .nav-menu > li > a {
  padding: 22px 58px;
  height: 100% !important;
  font-size: 14px;
  font-weight: 500 !important;
  color: #383838;
  text-transform: uppercase !important;
}

.nav-menu-item.nav-icon-item .nav-menu > li > a i {
  display: block;
  width: auto;
  text-align: center;
  margin-bottom: 12px;
  font-size: 35px;
  height: auto;
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
  line-height: 34px;
}

.nav-menu-item.nav-icon-item .nav-menu > li .nav-dropdown li a:hover {
  color: #fff;
}

.nav-menu-item.nav-icon-item .ts-main-menu .right-menu li .nav-search .nav-search-button {
  width: auto;
  line-height: 36px;
  padding: 25px 61px;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
}

.nav-menu-item.nav-icon-item .ts-main-menu .right-menu li .nav-search .nav-search-button i {
  font-size: 24px;
}

.nav-menu-item.nav-icon-item .ts-main-menu .right-menu li .nav-search .nav-search-button b {
  display: block;
  text-transform: uppercase;
  font-weight: 500;
}

.nav-menu-item.nav-icon-item .ts-main-menu .right-menu li .nav-search .nav-search-button:hover {
  color: #6cba40;
}

/*---- vertion 4---*/
.header-transparent {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 2;
  width: 100%;
  border-bottom: 1px solid #33335b;
}

.header-transparent .nav-header {
  width: auto;
}

.header-transparent .nav-header .nav-brand {
  padding: 0;
  line-height: 80px;
}

.header-transparent .navigation {
  background: transparent;
  height: 80px;
}

.header-transparent .nav-menu > li > a {
  color: #fff;
  height: 80px;
  padding: 31px 26px;
  position: relative;
}

.header-transparent .nav-menu > li > a .submenu-indicator {
  margin-top: 0;
}

.header-transparent .nav-menu > li > a:before {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 2px;
  background: #007dff;
  content: '';
  opacity: 0;
  visibility: hidden;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -webkit-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
}

.header-transparent .nav-menu > li > a:hover {
  background: transparent;
}

.header-transparent .nav-menu > li > a:hover::before {
  opacity: 1;
  visibility: visible;
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
}

.header-transparent .nav-menu > li.active > a {
  background: transparent;
  color: #007dff;
}

.header-transparent .nav-menu > li .nav-dropdown {
  border-top: none;
  background: #fff;
}

.header-transparent .right-menu li .nav-search {
  width: 80px;
  height: 80px;
  margin-left: 15px;
}

.header-transparent .right-menu li .nav-search .nav-search-button {
  width: 100%;
  height: 100%;
  line-height: 80px;
  background: transparent;
  border-left: 1px solid #33335b;
  border-right: 1px solid #33335b;
}

.header-box .navigation {
  -webkit-box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.08);
  box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.08);
  height: 50px;
}

.header-box .right-menu li .nav-search {
  height: 50px;
}

.header-box .right-menu li .nav-search .nav-search-button {
  line-height: 56px;
}

.header-box .nav-menu > li > a {
  padding: 16px 25px;
  height: 50px;
}

.header-box .nav-menu > li > a:hover {
  background: #fff;
  color: #005689 !important;
}

.header-box .nav-menu > li > a:hover::before {
  opacity: 1;
  visibility: visible;
  width: 100%;
}

.header-box .nav-menu > li > a .submenu-indicator {
  margin-top: 0;
}

.header-box .nav-menu > li > a:before {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 0%;
  height: 2px;
  background: #005689;
  content: '';
  opacity: 0;
  visibility: hidden;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
}

.header-box .nav-menu > li.active > a {
  background: transparent;
  color: #005689;
}

.header-box .right-menu li .nav-search .nav-search-button {
  background: #fff;
  color: #232323;
}

.ts-mega-menu.megamenu-panel {
  max-width: 500px;
  padding: 15px 0;
}

.ts-mega-menu.megamenu-panel .megamenu-list li a {
  height: 46px;
  padding: 15px 20px;
}

/*------------------------------
    featured post area 
    ------------------------------*/
.ts-overlay-style {
  position: relative;
}

.ts-overlay-style .item:before {
  content: " ";
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  z-index: 1;
  bottom: 0;
  left: 0;
  background: -webkit-gradient(linear, left top, left bottom, color-stop(55%, rgba(0, 0, 0, 0)), to(rgba(0, 0, 0, 0.95)));
  background: -webkit-linear-gradient(top, rgba(0, 0, 0, 0) 55%, rgba(0, 0, 0, 0.95) 100%);
  background: -o-linear-gradient(top, rgba(0, 0, 0, 0) 55%, rgba(0, 0, 0, 0.95) 100%);
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 55%, rgba(0, 0, 0, 0.95) 100%);
}

.ts-overlay-style .item:after {
  background: rgba(0, 0, 0, 0.2);
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  z-index: 0;
  bottom: 0;
  left: 0;
  content: '';
  opacity: 0;
  -webkit-transition: all ease 500ms;
  -o-transition: all ease 500ms;
  transition: all ease 500ms;
}

.ts-overlay-style .item:hover:after {
  opacity: 1;
}

.ts-overlay-style.ts-featured .item {
  min-height: 573px;
  position: relative;
  background-position: 50% 50%;
  -webkit-background-size: cover;
  background-size: cover;
  -webkit-backface-visibility: hidden;
}

.ts-overlay-style.ts-featured .item .post-content {
  padding: 40px;
}

.ts-overlay-style.ts-featured .item .post-title {
  margin-bottom: 24px;
}

.ts-overlay-style .post-meta-info {
  margin-bottom: 0;
}

#featured-slider-2 .item {
  min-height: 418px;
}

#featured-slider-2 .owl-dots {
  right: 20px;
  top: 5px;
}

#featured-slider-2 .owl-dots .owl-dot span {
  background: #fff;
}

#featured-slider-2 .post-title.lg {
  font-size: 30px;
}

.post-content {
  z-index: 1;
  position: relative;
}

.post-content p {
  font-size: 15px;
  line-height: 24px;
  color: #232323;
}

.ts-overlay-style .overlay-post-content {
  position: absolute;
  bottom: 0;
}

.ts-overlay-style .overlay-post-content .post-content {
  padding: 24px 24px 18px;
}

.ts-overlay-style .overlay-post-content .post-date-info {
  color: #d2d2d2;
}

.ts-overlay-style .overlay-post-content .post-title {
  margin-bottom: 5px;
}

.ts-overlay-style .overlay-post-content .post-title a {
  color: #fff;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
}

.ts-overlay-style .overlay-post-content .post-title.md {
  margin-bottom: 10px;
}

.ts-overlay-style .overlay-post-content .post-meta-info li {
  color: #fff;
}

.ts-overlay-style .overlay-post-content .post-meta-info li a {
  color: #fff;
}

.ts-overlay-style .overlay-post-content .post-meta-info li a:hover {
  color: #d72924;
}

.ts-overlay-style .overlay-post-content .post-meta-info li.active {
  color: #d72924;
}

.post-cat {
  position: relative;
  z-index: 1;
  display: inline-block;
  color: #fff;
  font-size: 11px;
  font-weight: 700;
  text-transform: uppercase;
  padding: 0px 10px;
  margin-left: 40px;
  line-height: 21px;
  height: 19px;
  top: -3px;
  letter-spacing: .55px;
}

.post-cat:hover {
  color: #fff;
}

.cat-name {
  font-size: 11px;
  color: #6cba40;
  font-weight: 700;
  text-transform: uppercase;
  display: block;
  margin-bottom: 5px;
}

.post-date-info {
  font-size: 12px;
  display: block;
  position: relative;
  color: #8a8a8a;
}

/*--------- single post ---*/
.ts-grid-box {
  position: relative;
  margin-bottom: 30px;
  padding: 30px;
  background: #fff;
  -webkit-box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.08);
  box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.08);
  /*.post-content{
        padding: 20px;
        background: $white-color;
        box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.08);
      }*/
}

.ts-grid-box.ts-grid-content {
  padding: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.ts-grid-box.ts-grid-content .post-content {
  padding: 0 20px 18px 20px;
  -webkit-box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.08);
  box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.08);
}

.ts-grid-box.ts-grid-content:hover .ts-post-thumb img {
  -webkit-transform: scale(1.2);
  -ms-transform: scale(1.2);
  transform: scale(1.2);
}

.ts-grid-box .post-cat {
  position: absolute;
  margin-left: 30px;
  top: 0;
}

.ts-grid-box .ts-post-thumb {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  margin-bottom: 20px;
  min-height: 10px;
}

.ts-grid-box .ts-post-thumb img {
  width: 100%;
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

.ts-grid-box .ts-post-thumb:hover img {
  -webkit-transform: scale(1.2);
  -ms-transform: scale(1.2);
  transform: scale(1.2);
}

.ts-grid-box:last-of-type {
  margin-bottom: 0;
}

.ts-grid-box.ts-grid-content-1 .post-cat {
  position: relative;
  margin-bottom: 16px;
  margin-left: 0;
}

.ts-grid-box.ts-grid-content-1 .ts-post-thumb {
  margin-bottom: 0;
}

.ts-grid-box.ts-grid-content-1 .post-content {
  padding: 20px 22px 18px 22px;
}

.ts-grid-box.ts-grid-content-1 .post-meta-info li a:hover {
  color: #005689;
}

.ts-grid-box.ts-grid-content-1.featured-item .post-content {
  padding: 20px 28px 18px 28px;
}

.ts-grid-box.ts-grid-content-1.featured-item .ts-post-thumb .link-img {
  min-height: 290px;
}

.ts-post-overlay-style-1 {
  background: transparent;
  padding: 0;
}

.ts-post-overlay-style-1 .ts-post-thumb {
  margin-bottom: 10px;
}

.ts-post-overlay-style-1 .ts-post-thumb img {
  min-height: 204px;
}

.ts-post-overlay-style-1 .ts-overlay-style:last-of-type .ts-post-thumb {
  margin-bottom: 0;
}

/*------- post tab list ---*/
.post-list-item {
  position: relative;
  background: #fff;
}

.post-list-item .nav-tabs {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin-bottom: 6px;
}

.post-list-item .nav-tabs li {
  -ms-flex-preferred-size: 0;
  flex-basis: 0;
  -ms-flex-positive: 1;
  -webkit-box-flex: 1;
  flex-grow: 1;
  max-width: 100%;
  text-align: center;
}

.post-list-item .nav-tabs li a {
  font-size: 13px;
  text-transform: uppercase;
  color: #8a8a8a;
  line-height: 50px;
  font-weight: 500;
  position: relative;
  display: block;
}

.post-list-item .nav-tabs li a::before {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 2px;
  background: #6200ee;
  content: '';
  opacity: 0;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
}

.post-list-item .nav-tabs li a i {
  margin-right: 6px;
}

.post-list-item .nav-tabs li a.active {
  color: #6200ee;
}

.post-list-item .nav-tabs li a.active::before {
  opacity: 1;
}

.post-list-item.blue-dark .nav-tabs li a:before {
  background: #005689;
}

.post-list-item.blue-dark .nav-tabs li a.active {
  color: #005689;
}

.post-list-item.blue-dark .post-title a:hover {
  color: #005689;
}

/*---------- tab list -----*/
.post-tab-list {
  margin-bottom: 0;
  padding: 15px;
}

.post-tab-list .post-content.media {
  border-bottom: 1px solid #ededed;
  margin-bottom: 16px;
  padding-bottom: 15px;
}

.post-tab-list .post-content.media .post-title {
  margin-bottom: 0;
}

.post-tab-list .post-content.media:last-of-type {
  padding-bottom: 0;
  border-bottom: none;
  margin-bottom: 5px;
}

.post-tab-list .post-tag {
  line-height: 10px;
  margin-bottom: 6px;
  display: block;
}

.post-tab-list .post-tag a {
  font-size: 11px;
  text-transform: uppercase;
  font-weight: 700;
}

img.sidebar-img {
  width: 70px;
  height: 50px;
  margin-right: 15px;
}

/*---------------------------
      hot topics
  ---------------------------*/
#hot-topics-slider {
  margin-bottom: -60px;
}

.heighlight {
  background: #007dff;
}

.heighlight .ts-post-thumb .post-cat {
  position: absolute;
  margin-left: 0;
  top: auto;
  bottom: 0;
  padding: 4px 15px 0 20px;
}

.heighlight .ts-post-thumb .post-cat:before {
  border-style: solid;
  border-width: 28px 0 0 20px;
  border-color: transparent transparent transparent #007bff;
  position: absolute;
  right: -20px;
  top: 0;
  content: '';
}

.heighlight .post-content {
  padding: 0 20px 20px;
}

.heighlight .post-content .post-title a {
  color: #fff;
}

.heighlight .post-content .post-title a:hover {
  opacity: .8;
}

.heighlight .post-content .post-meta-info li {
  color: #fff;
}

.heighlight .post-content .post-date-info {
  color: #fff;
}

.heighlight.ts-green-heighlight {
  background: #4ab106;
}

.heighlight.ts-green-heighlight .post-cat {
  background: #4ab106;
}

.heighlight.ts-green-heighlight .post-cat:before {
  border-color: transparent transparent transparent #4ab106;
}

.heighlight.ts-blue-heighlight {
  background: #007dff;
}

.heighlight.ts-blue-heighlight .post-cat {
  background: #007dff;
}

.heighlight.ts-blue-heighlight .post-cat:before {
  border-color: transparent transparent transparent #007dff;
}

.heighlight.ts-blue-light-heighlight {
  background: #007dff;
}

.heighlight.ts-blue-light-heighlight .post-cat {
  background: #007dff;
}

.heighlight.ts-blue-light-heighlight .post-cat:before {
  border-color: transparent transparent transparent #007dff;
}

.heighlight.ts-pink-heighlight {
  background: #ff5575;
}

.heighlight.ts-pink-heighlight .post-cat {
  background: #ff5575;
}

.heighlight.ts-pink-heighlight .post-cat:before {
  border-color: transparent transparent transparent #ff5575;
}

.heighlight.ts-yellow-heighlight {
  background: #ffaf31;
}

.heighlight.ts-yellow-heighlight .post-cat {
  background: #ffaf31;
}

.heighlight.ts-yellow-heighlight .post-cat:before {
  border-color: transparent transparent transparent #ffaf31;
}

/*----------------------------
      watch now featured 
  ------------------------------*/
.ts-video-btn {
  position: absolute;
  bottom: 0;
  font-size: 80px;
  right: 0;
  left: 0;
  top: 100px;
  margin: auto;
  display: block;
  text-align: center;
  color: #fff;
  z-index: 1;
}

.ts-video-btn:hover {
  color: #fff;
}

.post-list-box .nav-link {
  padding: 20px 0;
  border-bottom: 1px solid #e7e7e7;
}

.post-list-box .nav-link:first-of-type {
  padding-top: 0;
}

.post-list-box .nav-link:last-of-type {
  border-bottom: none;
  padding-bottom: 0;
}

.post-list-box .post-content img {
  width: 128px;
  height: 85px;
  margin-right: 15px;
}

.post-list-box .post-content .post-title:hover {
  color: #d72924;
}

.post-list-box.ts-list-post-box.ts-grid-content .post-content img {
  width: 110px;
  height: auto;
  margin-right: 15px;
}

.watch-post .ts-overlay-style .item {
  min-height: 343px;
}

.item:hover .ts-post-thumb img {
  -webkit-transform: scale(1.2);
  -ms-transform: scale(1.2);
  transform: scale(1.2);
}

/*---------------------------------
    tranding post 
------------------------------------*/
.ts-grid-box.ts-col-box {
  padding: 40px 15px 22px 20px;
}

.ts-grid-box.ts-col-box .post-cat {
  margin-left: 0;
}

.ts-grid-box.ts-col-box .ts-title:before {
  left: -15px;
}

.ts-grid-box.ts-col-box .item {
  margin-bottom: 26px;
}

.ts-grid-box.ts-col-box .item:last-of-type {
  margin-bottom: 0;
}

.ts-grid-box.ts-col-box .post-date-info {
  display: block;
}

.ts-grid-box.ts-col-box .post-content p {
  margin-top: 8px;
}

.ts-tranding-post {
  padding: 30px 0 0 0;
}

.ts-tranding-post .ts-post-thumb {
  margin-bottom: 0;
}

.ts-tranding-post .ts-title {
  margin-left: 30px;
}

.ts-tranding-post .ts-arrow {
  position: absolute;
  right: 15px;
  top: 28px;
}

.ts-tranding-post .ts-arrow a {
  padding: 0 8px;
  color: #101010;
  display: inline-block;
  position: relative;
  font-size: 20px;
}

.ts-tranding-post .ts-arrow a:hover {
  color: #d72924;
}

.ts-tranding-post .ts-arrow a.control-prev::before {
  position: absolute;
  right: -2px;
  top: -3px;
  width: 1px;
  height: 17px;
  background: #ddd;
  content: '';
  bottom: 0;
  margin: auto;
  display: block;
  text-align: center;
}

.ts-tranding-post .slider-indicators {
  background: #2e55bd;
}

.ts-tranding-post .slider-indicators.carousel-indicators {
  list-style: none;
  padding: 0;
  position: relative;
  display: block;
  margin: 0;
  width: 100%;
  bottom: 0;
}

.ts-tranding-post .slider-indicators.carousel-indicators > li {
  float: left;
  cursor: pointer;
  width: 50%;
  display: inline-block;
  margin: 0;
  height: auto;
  text-indent: 1px;
  background: transparent;
}

.ts-tranding-post .slider-indicators.carousel-indicators > li:before {
  position: absolute;
  right: -4px;
  left: auto;
  display: block;
  width: 1px;
  height: 48px;
  content: "";
  background: #233f88;
  bottom: 0;
  top: 0;
  margin: auto;
}

.ts-tranding-post .slider-indicators.carousel-indicators > li:after {
  position: absolute;
  right: -3px;
  left: auto;
  display: block;
  width: 1px;
  height: 48px;
  content: "";
  background: #6c7ca5;
  bottom: 0;
  top: 0;
  margin: auto;
}

.ts-tranding-post .slider-indicators.carousel-indicators > li:last-child:before, .ts-tranding-post .slider-indicators.carousel-indicators > li:last-child:after {
  display: none;
}

.ts-tranding-post .slider-indicators.carousel-indicators > li.active {
  border-bottom: 4px solid #ffaf31;
}

.ts-tranding-post .slider-indicators .post-content {
  padding: 20px 20px 17px;
}

.ts-tranding-post .slider-indicators .post-content .post-count {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -ms-border-radius: 50%;
  border: 1px solid #adc3ff;
  font-size: 20px;
  text-align: center;
  color: #fff;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-item-align: center;
  align-self: center;
  /* margin: auto; */
  display: block;
  -ms-flex-line-pack: center;
  align-content: center;
  padding: 0 17px;
  margin-right: 10px;
  background: #2b50b1;
}

/*-------------------------- social list ----*/
.ts-social-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.ts-social-list li {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 33.3%;
  flex: 0 0 33.3%;
  max-width: 33.3%;
  text-align: center;
  font-size: 13px;
  font-weight: 500;
}

.ts-social-list li a i {
  font-size: 16px;
  width: 45px;
  height: 45px;
  display: block;
  text-align: center;
  color: #fff;
  border-radius: 4px;
  -webkit-border-radius: 4px;
  -ms-border-radius: 4px;
  padding: 15px 0;
  margin: auto auto 8px;
}

.ts-social-list li b {
  display: block;
  color: #222222;
  margin-bottom: 2px;
  line-height: 13px;
  font-size: 13px;
  font-weight: 500;
}

.ts-social-list li span {
  display: block;
  color: #a9a9a9;
  font-size: 13px;
  line-height: 19px;
}

/*-------------- right sidebar ---*/
.widgets {
  margin-bottom: 30px;
}

.widgets .ts-social-list li {
  margin-bottom: 15px;
}

.widgets .ts-social-list li:nth-last-child(1), .widgets .ts-social-list li:nth-last-child(2), .widgets .ts-social-list li:nth-last-child(3) {
  margin-bottom: 0;
}

.widgets:last-of-type {
  margin-bottom: 0;
}

.widgets .ts-overlay-style .item {
  min-height: inherit;
}

.widgets .ts-overlay-style .item .post-content {
  padding: 13px 20px;
}

.widgets ul li {
  font-size: 14px;
  color: #232323;
}

.widgets ul li a {
  font-size: 14px;
  color: #232323;
}

.widgets ul li a:hover {
  color: #d72924;
}

.widgets .category-list li {
  display: block;
  clear: both;
  margin-bottom: 20px;
}

.widgets .category-list li a span {
  float: right;
  color: #fff;
  padding: 1px 5px;
  display: inline-block;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -ms-border-radius: 50%;
}

.widgets .category-list li:last-child {
  margin-bottom: 0;
}

.widgets.post-tab-list .widget-title:before {
  left: -15px;
}

.widgets.post-tab-list .post-content.media {
  margin-bottom: 10px;
  padding-bottom: 10px;
}

.widgets.post-tab-list .post-content.media:last-of-type {
  margin-bottom: 0;
  padding-bottom: 0px;
}

.widgets.tag-list ul li {
  display: inline-block;
}

.widgets.tag-list ul li a {
  border: 1px solid #e7e7e7;
  padding: 5px 10px;
  border-radius: 36px;
  -webkit-border-radius: 36px;
  -ms-border-radius: 36px;
  line-height: 36px;
}

.widgets.tag-list ul li a:hover {
  background: #d72924;
  border-color: #d72924;
  color: #fff;
}

.widgets.ts-grid-box.ts-col-box {
  padding: 30px 15px 22px;
}

.widgets.widgets-populer-post {
  padding: 30px 15px 20px;
}

.widgets.widgets-populer-post .widget-title {
  margin-left: 15px;
}

.widgets.widgets-populer-post .post-content.media {
  margin-bottom: 12px;
  padding-bottom: 12px;
}

.widgets.widgets-populer-post .post-content.media:last-of-type {
  margin-bottom: 0;
  padding-bottom: 0;
}

.widgets.ts-social-list-item ul {
  margin: -4px 0;
}

.widgets.ts-social-list-item ul li {
  display: inline-block;
}

.widgets.ts-social-list-item ul li a {
  text-align: center;
  display: block;
  min-width: 57px;
  font-size: 13px;
  padding: 8px 0;
  margin: 4px 2px;
  color: #fff;
}

.widgets.ts-social-list-item ul li a i {
  display: block;
  font-size: 16px;
  margin-bottom: 9px;
}

.widgets.ts-social-list-item ul li a b,
.widgets.ts-social-list-item ul li a span {
  display: block;
  margin-bottom: 0;
  line-height: 13px;
  cursor: pointer;
}

.widgets.ts-social-list-item ul li a b {
  font-weight: 600;
  margin-bottom: 4px;
}

.widgets.ts-social-list-item ul li a span {
  font-size: 10px;
}

.widgets.ts-social-list-item ul .ts-facebook a {
  background: #3b5999;
}

.widgets.ts-social-list-item ul .ts-twitter a {
  background: #55acee;
}

.widgets.ts-social-list-item ul .ts-google-plus a {
  background: #dd4b39;
}

.widgets.ts-social-list-item ul .ts-linkedin a {
  background: #007bb6;
}

.widgets.ts-social-list-item ul .ts-pinterest a {
  background: #bd081c;
}

.widgets.ts-social-list-item ul .ts-youtube a {
  background: #f33220;
}

.widgets.ts-social-list-item ul .ts-instragram a {
  background: #517fa3;
}

.widgets.ts-social-list-item ul .ts-dribble a {
  background: #ea4c89;
}

.widgets.ts-social-list-item ul .ts-behance a {
  background: #0079fe;
}

.widgets.widgets-item .widget-title {
  margin-left: 20px;
}

.widgets.widgets-item .widget-title:before {
  left: -20px;
  background: #6cba40;
}

.widgets .ts-widget-newsletter {
  background: #d72924;
  padding: 30px;
}

.widgets .ts-widget-newsletter .newsletter-introtext h4 {
  font-size: 20px;
  color: #fff;
  font-weight: 500;
  margin-bottom: 10px;
}

.widgets .ts-widget-newsletter .newsletter-introtext p {
  color: #fff;
  font-size: 15px;
  margin-bottom: 18px;
}

.widgets .ts-widget-newsletter .newsletter-form .form-group {
  margin-bottom: 0;
}

.widgets .ts-widget-newsletter .newsletter-form .form-control {
  width: 100%;
  border-radius: 0;
  outline: none;
  margin-bottom: 25px;
  height: 40px;
  font-size: 12px;
}

.widgets .ts-widget-newsletter .newsletter-form .btn {
  background: #861714;
  padding: 12px 30px;
}

.widgets .ts-widget-newsletter .newsletter-form .btn:hover {
  background: #d72924;
}

.widgets.single-widget .widget-title {
  margin-left: 20px;
}

.widgets.single-widget .widget-title:before {
  left: -20px;
  background: #007dff;
}

.widgets.single-widget .post-tab-list {
  padding: 0;
}

.widgets .blue-dark-heighlight .post-cat {
  color: #005689;
}

.widgets .blue-dark-heighlight .post-content .post-title a {
  color: #fff;
}

.widgets .blue-dark-heighlight .post-content .post-title a:hover {
  color: #fff;
}

.widgets .blue-dark-heighlight .post-content .post-title.md {
  margin-bottom: 15px;
}

.widgets .blue-dark-heighlight .post-content p {
  color: #fff;
}

.widgets .blue-dark-heighlight .post-content .post-date-info {
  color: #a3b7ca;
}

.widget-banner img,
.posts-ad img {
  max-width: 100%;
}

.ts-block-social-list li {
  margin-bottom: 10px;
}

.ts-block-social-list li:last-child {
  margin-bottom: 0;
}

.ts-block-social-list li a {
  display: block;
  padding: 7px 10px;
  color: #fff;
  position: relative;
}

.ts-block-social-list li a i {
  width: 34px;
  height: 34px;
  border-radius: 2px;
  -webkit-border-radius: 2px;
  -ms-border-radius: 2px;
  text-align: center;
  color: #fff;
  padding: 10px 0;
  margin-right: 5px;
}

.ts-block-social-list li a b,
.ts-block-social-list li a span {
  color: #fff;
  font-weight: 500;
  text-transform: capitalize;
}

.ts-block-social-list li a span {
  position: absolute;
  right: 10px;
  top: 14px;
  bottom: 0;
  margin: auto;
  font-weight: 400;
}

.ts-block-social-list li.ts-facebook a {
  background: #3b5999;
}

.ts-block-social-list li.ts-facebook a i {
  background: #324c82;
}

.ts-block-social-list li.ts-twitter a {
  background: #55acee;
}

.ts-block-social-list li.ts-twitter a i {
  background: #4892cb;
}

.ts-block-social-list li.ts-google-plus a {
  background: #dd4b39;
}

.ts-block-social-list li.ts-google-plus a i {
  background: #bc4031;
}

.ts-block-social-list li.ts-pinterest a {
  background: #bd081c;
}

.ts-block-social-list li.ts-pinterest a i {
  background: #a10718;
}

.ts-block-social-list li.ts-linkedin a {
  background: #007bb6;
}

.ts-block-social-list li.ts-linkedin a i {
  background: #11638a;
}

.ts-block-social-list li.ts-youtube a {
  background: #f33220;
}

.ts-block-social-list li.ts-youtube a i {
  background: #ce3425;
}

.widget-title {
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 25px;
  position: relative;
}

.widget-title:before {
  position: absolute;
  left: -30px;
  top: 0;
  width: 3px;
  height: 100%;
  content: '';
  background: #d72924;
}

.widgets-title {
  margin-bottom: 30px;
  position: relative;
}

.widgets-title span {
  position: relative;
  font-size: 14px;
  font-weight: 400;
  background: #6cba40;
  color: #fff;
  line-height: 26px;
  padding: 0 5px;
  padding: 2px 15px;
  display: inline-block;
}

.widgets-title span:before {
  position: absolute;
  width: 20px;
  height: 100%;
  content: '';
  background: #fff;
  right: -20px;
  top: 0;
}

.widgets-title:after {
  position: absolute;
  right: 0;
  bottom: 13px;
  width: 100%;
  height: 1px;
  background: #84be38;
  content: '';
  z-index: -1;
}

.topic-list {
  text-align: center;
  position: relative;
  display: block;
  margin: -30px auto 30px;
  border: none;
  background: #d72924;
  color: #fff;
  padding: 10px 40px;
  outline: none;
  font-size: 15px;
  font-weight: 700;
  text-transform: uppercase;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  min-width: 200px;
}

/*------------------------
   more news 
--------------------------*/
.ts-grid-box-heighlight {
  background: #7f85ff;
}

.ts-grid-box-heighlight .ts-title {
  color: #fff;
}

.ts-grid-box-heighlight .ts-title::before {
  background: #fff;
}

#more-news-slider {
  margin-bottom: -80px;
}

/*-----------------------
  footer social
--------------------------*/
.ts-footer-social-list {
  background: #fff;
  padding: 30px 0;
  margin-bottom: 5px;
  margin-top: 3px;
}

.ts-footer-social-list.section-bg {
  background: #f7f7f7;
}

.footer-social li {
  display: inline-block;
  margin-right: 38px;
}

.footer-social li a {
  font-size: 13px;
  color: #fff;
  font-weight: 500;
  color: #888888;
  text-transform: uppercase;
}

.footer-social li a i {
  font-size: 16px;
  color: #fff;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -ms-border-radius: 50%;
  text-align: center;
  padding: 9px 0px;
  margin-right: 8px;
}

.footer-social li a span:hover {
  color: #222;
}

.footer-social li:last-child {
  margin-right: 0;
}

.footer-social-list li {
  display: inline-block;
}

.footer-social-list li a {
  font-size: 14px;
  margin-left: 18px;
}

.footer-social-list li a i {
  background: transparent;
}

.footer-social-list li.ts-facebook a {
  color: #3b5999;
}

.footer-social-list li.ts-twitter a {
  color: #55acee;
}

.footer-social-list li.ts-google-plus a {
  color: #dd4b39;
}

.footer-social-list li.ts-pinterest a {
  color: #bd081c;
}

.footer-social-list li.ts-youtube a {
  color: #f33220;
}

.footer-social-list li.ts-linkedin a {
  color: #007bb6;
}

/*-----------------------------
    ts-newslatter
 ------------------------------*/
.ts-newslatter {
  background: #fff;
  padding: 40px 0;
}

.ts-newslatter.section-bg {
  background: #f7f7f7;
}

.ts-newslatter.section-bg .newsletter-form .email-form-group .form-control {
  background: transparent;
}

.ts-newslatter .ts-newslatter-content h2 {
  margin-bottom: 17px;
  font-size: 30px;
  font-weight: 500;
}

.ts-newslatter .ts-newslatter-content p {
  margin-bottom: 0;
  padding-right: 50px;
  color: #888;
}

.ts-newslatter .newsletter-form .email-form-group {
  padding-left: 40px;
  position: relative;
}

.ts-newslatter .newsletter-form .email-form-group i {
  position: absolute;
  left: 0;
  font-size: 20px;
  top: 15px;
}

.ts-newslatter .newsletter-form .email-form-group .form-control {
  border: none;
  outline: none;
  border-bottom: 1px solid #a9a9a9;
  border-radius: 0;
  -webkit-border-radius: 0;
  -ms-border-radius: 0;
  padding: 9px 0;
  font-size: 12px;
}

.ts-newslatter .newsletter-form .email-form-group .form-control:focus {
  outline: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.ts-newslatter .newsletter-form .email-form-group .form-control::-webkit-input-placeholder {
  letter-spacing: 1.2px;
}

.ts-newslatter .newsletter-form .email-form-group .form-control:-ms-input-placeholder {
  letter-spacing: 1.2px;
}

.ts-newslatter .newsletter-form .email-form-group .form-control::-ms-input-placeholder {
  letter-spacing: 1.2px;
}

.ts-newslatter .newsletter-form .email-form-group .form-control::placeholder {
  letter-spacing: 1.2px;
}

.ts-newslatter .newsletter-form .ts-submit-btn {
  margin-left: 20px;
}

.ts-newslatter .newsletter-form .ts-submit-btn .btn {
  background: #d66f2a;
}

.ts-newslatter .newsletter-form .ts-submit-btn .btn:hover {
  background: #9b4710;
}

/*---------------------------
    ts-footer
 --------------------------*/
.footer-menu {
  margin-bottom: 14px;
}

.footer-menu ul li {
  display: inline-block;
}

.footer-menu ul li a {
  font-size: 14px;
  color: #fff;
  margin: 0 22px;
  opacity: .7;
}

.footer-menu ul li a:hover {
  opacity: 1;
}

.ts-footer {
  background: #4e65ff;
  padding: 25px 0;
  position: relative;
}

.ts-footer .back-to-top {
  position: fixed;
  right: 60px;
  bottom: 40px;
  z-index: 10;
  -webkit-backface-visibility: hidden;
}

.ts-footer .back-to-top .btn.btn-primary {
  width: 30px;
  height: 30px;
  line-height: 30px;
  font-weight: 700;
  font-size: 16px;
  padding: 0;
}

.ts-footer.ts-footer-1 {
  background: #f8f8f8;
  padding-bottom: 15px;
}

.ts-footer.ts-footer-1 .footer-social {
  padding-bottom: 22px;
  border-bottom: 1px solid #ddd;
}

.ts-footer.ts-footer-1 .footer-social li a i {
  background: transparent;
  color: #888888;
  width: auto;
  height: auto;
}

.ts-footer.ts-footer-1 .footer-social li a span {
  letter-spacing: 1px;
}

.ts-footer.ts-footer-1 .copyright-text {
  padding-top: 15px;
}

.ts-footer.ts-footer-1 .copyright-text p {
  color: #888;
}

.ts-footer.ts-footer-1 .copyright-text p a {
  color: #6cba40;
  text-transform: uppercase;
}

.ts-footer.ts-footer-2 {
  background: transparent;
}

.ts-footer.ts-footer-2 .copyright-text p {
  color: #232323;
}

.ts-footer.ts-footer-2 .copyright-text p a {
  color: #6cba40;
}

.ts-footer.ts-footer-3 {
  background: #000032;
}

.ts-footer.ts-footer-3 .footer-logo {
  padding: 34px 0 44px;
}

.ts-footer.ts-footer-3 .footer-menu {
  margin-bottom: 22px;
}

.ts-footer.ts-footer-3 .footer-menu li a {
  text-transform: uppercase;
  opacity: 1;
  font-weight: 700;
  position: relative;
}

.ts-footer.ts-footer-3 .footer-menu li a:hover {
  color: #007dff;
}

.ts-footer.ts-footer-3 .footer-menu li a:after {
  position: absolute;
  right: -24px;
  top: 4px;
  width: 1px;
  height: 12px;
  background: #4d4d70;
  content: '';
  -webkit-transform: rotate(-20deg);
  -ms-transform: rotate(-20deg);
  transform: rotate(-20deg);
}

.ts-footer.ts-footer-3 .footer-menu li:last-child a:after {
  display: none;
}

.ts-footer.ts-footer-3 .copyright-text {
  margin-top: 32px;
}

.ts-footer.ts-footer-3 .copyright-text p {
  font-size: 14px;
  font-weight: 700;
  text-transform: uppercase;
  opacity: 1;
  color: #fff;
}

.ts-footer.ts-footer-3 .copyright-text span, .ts-footer.ts-footer-3 .copyright-text a {
  font-size: 14px;
  font-weight: 400;
  color: #6f6f8b;
}

.ts-footer.ts-footer-3 .footer-social-list {
  margin-top: 32px;
}

.ts-footer.ts-footer-3 .footer-social-list li a {
  color: #fff;
  margin-left: 54px;
}

.ts-footer.ts-footer-3 .footer-social-list li a:hover {
  color: #007dff;
}

.copyright-text p {
  color: #ffffff;
  margin-bottom: 0;
  font-size: 14px;
}

/*------------------------------ 
      single post page 
-------------------------------*/
.single-post-wrapper {
  padding: 30px 0;
}

/*-------------- breadcrumb ---------------- */
.post-featured-image {
  position: relative;
}

.post-featured-image img {
  width: 100%;
  max-height: 550px;
}

.breadcrumb {
  background: #fff;
  webkit-box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.08);
  -webkit-box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.08);
  box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.08);
  padding: 0;
  border-radius: 0;
}

.breadcrumb li {
  display: inline-block;
  padding: 7px 34px 7px 17px;
  color: #d72924;
  position: relative;
}

.breadcrumb li:before {
  position: absolute;
  right: 0;
  top: 0;
  content: '';
  background: url(../images/icon/angle.png) no-repeat center center/contain;
  width: 20px;
  height: 100%;
}

.breadcrumb li a {
  font-size: 13px;
  color: #5c5c5c;
}

.breadcrumb li a i {
  margin-right: 8px;
}

.ts-breadcrumb {
  padding: 0;
}

.ts-breadcrumb li {
  display: inline-block;
  list-style: none;
  font-size: 13px;
  color: #a9a9a9;
}

.ts-breadcrumb li a {
  color: #a9a9a9;
}

.ts-breadcrumb > li + li:before {
  content: "\f105";
  font-family: FontAwesome;
  padding: 0 6px;
  color: #777;
}

.single-post.content-wrapper,
.comments-form.ts-grid-box {
  padding: 30px 80px 40px;
}

.single-post .post-meta-info {
  margin-bottom: 35px;
  padding-left: 0;
}

.single-post .post-meta-info li.author {
  padding-left: 60px;
}

.single-post .post-meta-info li.author a img {
  top: -6px;
  width: 40px;
  height: 40px;
}

.single-post .post-meta-info li a.post-cat {
  position: relative;
}

.single-post .post-meta-info li.share-post a {
  color: #fff;
}

.single-post .post-meta-info li.share-post a i {
  font-size: 16px;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -ms-border-radius: 50%;
  text-align: center;
  padding: 9px 0px;
  margin-right: 0px;
  background: #d72924;
}

.single-post .post-media {
  margin: 0 -80px;
}

.single-post p {
  font-size: 16px;
  line-height: 28px;
  color: #232323;
  margin-bottom: 25px;
}

.single-post p span {
  background: #fff9b5;
  padding: 5px 6px;
}

.single-post .text-bg {
  background: #f7f7f7;
  font-size: 13px;
  font-style: italic;
  text-align: center;
  padding: 9px;
  margin-bottom: 30px;
}

.single-post .entry-content .tie-dropcap {
  color: #a7a7a7;
  float: left;
  font-size: 120px;
  line-height: 80px;
  font-weight: 700;
  background: transparent;
  margin-right: 17px;
  padding: 0;
}

.single-post .entry-content h3 {
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 26px;
  line-height: 28px;
}

.single-post ul {
  margin-bottom: 25px;
  padding-left: 20px;
}

.single-post ul li {
  font-size: 16px;
  line-height: 28px;
  color: #232323;
  list-style: inside;
}

.single-post .gallery-img {
  position: relative;
  padding: 20px 0;
  border-top: 1px solid #dddddd;
  border-bottom: 1px solid #dddddd;
  margin-bottom: 30px;
}

.single-post .gallery-img:after {
  display: block;
  clear: both;
  content: "";
}

.single-post .gallery-img img {
  float: left;
  width: 20%;
}

p img.float-left {
  margin-right: 30px;
  margin-bottom: 30px;
}

.post-video {
  position: relative;
  margin-bottom: 35px;
}

.post-video img {
  width: 100%;
}

.post-video:before {
  content: " ";
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  z-index: 1;
  bottom: 0;
  left: 0;
  background: -moz-linear-gradient(bottom, rgba(0, 0, 0, 0) 14%, rgba(0, 0, 0, 0.85) 100%);
  background: -webkit-gradient(linear, left top, left bottom, color-stop(60%, rgba(0, 0, 0, 0)), color-stop(100%, rgba(0, 0, 0, 0.85)));
  background: -webkit-linear-gradient(bottom, rgba(0, 0, 0, 0) 14%, rgba(0, 0, 0, 0.85) 100%);
  background: -o-linear-gradient(bottom, rgba(0, 0, 0, 0) 14%, rgba(0, 0, 0, 0.85) 100%);
  background: -ms-linear-gradient(bottom, rgba(0, 0, 0, 0) 14%, rgba(0, 0, 0, 0.85) 100%);
  background: -webkit-gradient(linear, left top, left bottom, color-stop(14%, rgba(0, 0, 0, 0)), to(rgba(0, 0, 0, 0.85)));
  background: -webkit-linear-gradient(top, rgba(0, 0, 0, 0) 14%, rgba(0, 0, 0, 0.85) 100%);
  background: -o-linear-gradient(top, rgba(0, 0, 0, 0) 14%, rgba(0, 0, 0, 0.85) 100%);
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 14%, rgba(0, 0, 0, 0.85) 100%);
}

.post-video .post-video-content {
  padding: 20px;
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 1;
}

.post-video .post-video-content h3 {
  margin-bottom: 0;
}

.post-video .post-video-content h3 a {
  color: #fff;
}

.post-video .post-video-content h3 a:hover {
  color: #d72924;
}

.post-video .ts-play-btn {
  position: relative;
  top: 0;
  left: 0;
  right: auto;
  font-size: 20px;
  width: 60px;
  height: 60px;
  border: 1px solid #92999a;
  border-radius: 50%;
  padding: 18px 0;
  display: block;
  text-align: center;
  font-size: 15px;
  background: #00292f;
  float: left;
  color: #fff;
  margin-right: 20px;
}

.post-video .ts-play-btn:hover {
  background: #d72924;
}

/* author box --*/
.author-box {
  position: relative;
  padding: 20px 0 20px 100px;
  border-top: 1px solid #e7e7e7;
  border-bottom: 1px solid #e7e7e7;
  margin-bottom: 30px;
}

.author-box .author-img {
  position: absolute;
  left: 0;
  top: 0;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -ms-border-radius: 50%;
  top: 16px;
}

.author-box .author-name {
  font-size: 14px;
  text-transform: uppercase;
  font-weight: 500;
  float: left;
}

.author-box p {
  margin-bottom: 0;
}

.author-box .authors-social {
  float: right;
}

.author-box .authors-social a {
  font-size: 14px;
  margin-left: 33px;
}

.author-box .authors-social a:first-of-type {
  margin-left: 0;
}

.author-box .authors-social a.ts-twitter {
  color: #55acee;
}

.author-box .authors-social a.ts-google-plus {
  color: #dd4b39;
}

.author-box .authors-social a.ts-facebook {
  color: #3b5999;
}

.author-box .authors-social a.ts-linkedin {
  color: #007bb6;
}

.author-box .authors-social a:hover {
  color: #d72924;
}

.author-box.author-box-item {
  border: none;
  padding: 0 0 0 100px;
  margin-bottom: 0;
}

.author-box.author-box-item .author-name a {
  text-transform: lowercase;
}

.author-box.author-box-item p {
  margin: 15px 0 20px;
}

.author-box.author-box-item .post-meta-info {
  margin-bottom: 0;
}

.author-box.author-box-item .post-meta-info li a {
  font-size: 14px;
  color: #232323;
}

/* post navigation --*/
.post-navigation a span {
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  display: block;
  color: #222222;
}

.post-navigation a p {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 0;
  line-height: 20px;
}

.post-navigation .post-previous,
.post-navigation .post-next {
  width: 50%;
  position: relative;
}

.post-navigation .post-previous:hover span,
.post-navigation .post-next:hover span {
  color: #d72924;
}

.post-navigation .post-next {
  text-align: right;
  padding-right: 120px;
  padding-left: 30px;
}

.post-navigation .post-next img {
  right: 0;
  left: auto;
}

.post-navigation .post-previous {
  padding-left: 120px;
  padding-right: 30px;
  border-right: 1px solid #e7e7e7;
}

.post-navigation .post-previous img {
  left: 0;
}

.post-navigation img {
  position: absolute;
  top: 0;
  width: 100px;
  height: 70px;
}

/*------------------------- 
comments-form
--------------------------*/
/* Comments reply */
.comments-list .comment-content {
  margin: 15px 0;
}

.comments-list .comment-reply {
  color: #252a37;
  font-weight: 400;
  font-size: 14px;
}

.comments-list .comment-reply i {
  margin-right: 5px;
}

.comments-list .comment-reply:hover {
  color: #d72924;
}

.comments-list {
  list-style: none;
  margin: 0;
  padding: 20px 0 0;
}

.comments-list .comments-reply {
  margin: 0 0 0 70px;
}

.comments-list .comment {
  padding-bottom: 20px;
  margin-bottom: 30px;
  border-bottom: 1px solid #e7e7e7;
}

.comments-list img.comment-avatar {
  width: 80px;
  height: 80px;
  border-radius: 100%;
  margin-right: 30px;
}

.comments-list .comment-body {
  margin-left: 110px;
}

.comments-list .comment-author {
  margin-bottom: 0;
  margin-top: 0;
  font-weight: 700;
  font-size: 18px;
  color: #252a37;
}

.comments-list .comment-date {
  color: #252a37;
  font-size: 14px;
  display: block;
  margin-top: -5px;
}

.comments-form.ts-grid-box {
  padding: 40px 80px 50px;
}

.comments-form .comment-reply-title {
  line-height: 32px;
  margin-bottom: 20px;
}

.comments-form .comment-form-cookies-consent {
  margin-bottom: 30px;
}

.comments-form .comment-form-cookies-consent label {
  font-size: 15px;
  color: #232323;
  cursor: pointer;
  display: inline;
}

.ts-form .form-group {
  margin-bottom: 26px;
}

.ts-form .form-group .form-control {
  height: 45px;
  width: 100%;
  border-radius: 0;
  -webkit-border-radius: 0;
  -ms-border-radius: 0;
}

.ts-form .form-group .form-control:focus {
  -webkit-box-shadow: none;
  box-shadow: none;
  border-color: #d72924;
}

.ts-form .form-group .form-control.msg-box {
  height: 185px;
  resize: none;
}

.view-all-link {
  font-size: 14px;
  color: #a9a9a9;
}

/*------------- single post style 2 ---------------*/
.category-name-list {
  margin-bottom: 20px;
}

.category-name-list .post-cat {
  position: relative;
  margin-left: 0;
  margin-right: 10px;
}

.post-layout-1 .post-meta-info {
  margin-bottom: 40px;
}

.post-layout-1 .post-meta-info li.author {
  padding-left: 60px;
}

.post-layout-1 .post-meta-info li.author a {
  text-transform: uppercase;
  font-weight: 500;
  color: #232323;
}

.post-layout-1 .post-meta-info li.author a img {
  width: 40px;
  height: 40px;
  top: -8px;
}

.post-layout-1 .breadcrumb {
  margin-bottom: 2px;
}

.post-layout-1 .single-post p span {
  background: transparent;
  color: #d72924;
}

.post-layout-1 .gallery-img {
  margin: 40px 0;
}

.single-big-img {
  min-height: 550px;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  position: relative;
  display: block;
}

.single-big-img .ts-video-btn {
  top: 50%;
  -webkit-transform: translateY(-15%);
  -ms-transform: translateY(-15%);
  transform: translateY(-15%);
}

.img-ovarlay:before {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  content: '';
  background: rgba(0, 0, 0, 0.3);
}

/*------------ post layout 3 ----------------*/
.post-layout-2 .single-big-img:before {
  position: absolute;
  left: 0;
  top: 0;
  background: -webkit-gradient(linear, left top, left bottom, color-stop(14%, rgba(0, 0, 0, 0)), to(rgba(0, 0, 0, 0.85)));
  background: -webkit-linear-gradient(top, rgba(0, 0, 0, 0) 14%, rgba(0, 0, 0, 0.85) 100%);
  background: -o-linear-gradient(top, rgba(0, 0, 0, 0) 14%, rgba(0, 0, 0, 0.85) 100%);
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 14%, rgba(0, 0, 0, 0.85) 100%);
  content: '';
  width: 100%;
  height: 100%;
}

.post-layout-2 .single-big-img .entry-header {
  position: absolute;
  left: 0;
  bottom: 0;
  padding: 30px 80px;
}

.post-layout-2 .single-big-img .entry-header .post-title {
  color: #fff;
}

.post-layout-2 .post-meta-info li a {
  color: #fff;
}

.post-layout-2 .post-meta-info li.author a {
  text-transform: uppercase;
}

.post-layout-2 .post-meta-info li.share-post a i {
  font-size: 16px;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -ms-border-radius: 50%;
  text-align: center;
  padding: 9px 0px;
  margin-right: 0px;
  background: #d72924;
}

.post-layout-2 .breadcrumb {
  margin-bottom: 2px;
}

/*--------------------post-layout-4 ------------*/
.post-layout-3 .content-wrapper.single-post {
  padding-top: 0;
}

.post-layout-7 {
  padding-top: 0;
}

.post-layout-7 .single-big-img:before {
  position: absolute;
  left: 0;
  top: 0;
  background: -webkit-gradient(linear, left top, left bottom, color-stop(14%, rgba(0, 0, 0, 0)), to(rgba(0, 0, 0, 0.85)));
  background: -webkit-linear-gradient(top, rgba(0, 0, 0, 0) 14%, rgba(0, 0, 0, 0.85) 100%);
  background: -o-linear-gradient(top, rgba(0, 0, 0, 0) 14%, rgba(0, 0, 0, 0.85) 100%);
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 14%, rgba(0, 0, 0, 0.85) 100%);
  content: '';
  width: 100%;
  height: 100%;
}

.post-layout-7 .single-big-img .entry-header-item {
  padding-top: 332px;
  padding-bottom: 40px;
}

.post-layout-7 .single-big-img .entry-header .post-title {
  color: #fff;
}

.post-layout-7 .post-meta-info li a {
  color: #fff;
}

.post-layout-7 .post-meta-info li.author a {
  text-transform: uppercase;
}

.post-layout-7 .post-meta-info li.share-post a i {
  font-size: 16px;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -ms-border-radius: 50%;
  text-align: center;
  padding: 9px 0px;
  margin-right: 0px;
  background: #d72924;
}

/*------------------ pos layout 6 -----------*/
.post-layout-6 .single-post.content-wrapper {
  margin-top: -150px;
}

/*-------------- post-layout-10--------*/
.post-layout-10 .entry-header {
  background: #fff;
  padding: 30px;
}

.post-layout-10 .entry-header .post-cat {
  margin-left: 0;
  margin-right: 15px;
  margin-bottom: 20px;
}

/*----------- ----------------------------------
      home 2
 ----------------------------------*/
.ts-grid-box.ts-category-title {
  padding: 18px 30px;
}

.ts-grid-box.ts-category-title .ts-title {
  margin-bottom: 0;
}

.ts-list-post-box .ts-post-thumb {
  margin-bottom: 0;
}

.ts-list-post-box.ts-grid-content .post-content {
  padding-top: 20px;
  padding-bottom: 15px;
  -webkit-box-shadow: none;
  box-shadow: none;
  border-bottom: 1px solid #f0f1f4;
}

.ts-list-post-box.ts-grid-content .item .post-title {
  margin-bottom: 5px;
}

.ts-list-post-box.ts-grid-content .item:last-of-type {
  -webkit-box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.08);
  box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.08);
}

.ts-list-post-box.ts-grid-content .item:last-of-type .post-content {
  border-bottom: none;
}

.post-list-box.ts-list-post-box.ts-grid-content .post-content:last-of-type {
  border-bottom: none;
}

/*-------- video slider ----*/
.flex {
  display: -webkit-flex;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-justify-content: flex-start;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.video-slider {
  overflow: hidden;
  position: relative;
}

.video-slider .slider-link.next {
  position: relative;
}

.video-slider .post-video {
  margin-bottom: 0;
}

.video-slider .post-video .post-video-content {
  padding: 40px;
}

.video-slider .post-video .post-video-content h3 {
  margin-bottom: 12px;
}

.video-slider .post-video .ts-play-btn {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  z-index: 1;
  background: #d72924;
  border-color: #d72924;
}

.video-slider .post-video img {
  max-height: 500px;
}

.video-slider .uk-position-small {
  position: absolute;
  left: 40px;
  bottom: 170px;
  z-index: 1;
  font-size: 19px;
  background: #373839;
  color: #fff;
  width: 28px;
  height: 28px;
  float: left;
  position: relative;
  margin: 0;
  padding: 3px 0;
  text-align: center;
}

.video-slider .uk-position-small::after {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 1px;
  height: 15px;
  background: #686869;
  content: '';
  margin: auto;
}

.video-slider .uk-position-small:last-of-type:after {
  display: none;
}

.video-slider .uk-position-small svg {
  width: 7px;
}

.category-style .ts-col-box.ts-grid-box {
  padding: 30px 15px 22px;
}

.category-style .ts-col-box.ts-grid-box .post-cat {
  margin-left: 20px;
}

.category-style .ts-col-box.ts-grid-box .ts-title {
  padding-left: 15px;
}

.ts-video-icon {
  position: absolute;
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  right: 0;
  margin: auto;
  display: block;
  text-align: center;
  font-size: 48px;
  color: #fff;
  z-index: 2;
}

.ts-video-icon:hover {
  color: #fff;
}

.category-box-item .ts-grid-box .ts-post-thumb img {
  max-height: 340px;
}

.category-post-item1 .ts-grid-box .ts-post-thumb img {
  max-height: 194px;
}

.category-item {
  padding-bottom: 24px;
}

.category-box-item-3 .item p {
  margin-bottom: 0;
}

.category-layout-1 {
  margin-bottom: -80px;
}

.category-layout-2 .ts-grid-box.ts-grid-content {
  margin-bottom: 30px;
}

.entry-cat-header .ts-title {
  margin-bottom: 0;
}

.category-layout-3 .post-meta-info li a {
  color: #232323;
  text-transform: uppercase;
}

.post-cat.no-bg {
  margin-left: 0;
  background: transparent;
  color: #d72924;
  margin-bottom: 10px;
}

.post-cat.no-bg.pink-color {
  color: #ff5575;
}

.post-cat.orange-color.no-bg {
  color: #ff6e0d;
}

.post-cat.blue-color.no-bg {
  color: #007dff;
}

.post-cat.green-color.no-bg {
  color: #4ab106;
}

.post-cat.yellow-color.no-bg {
  color: #ffaf31;
}

.category-layout-5 .ts-grid-box {
  -webkit-box-shadow: none;
  box-shadow: none;
}

.category-layout-6 .entry-cat-header .ts-title {
  margin-left: 30px;
}

.ts-post-thumb {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  margin-bottom: 20px;
}

.ts-post-thumb .post-cat {
  position: absolute;
  left: 0;
  top: 0;
  margin-left: 30px;
}

.ts-post-thumb img {
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
}

/*--------------------- ts-category-list----------*/
.ts-category-list li {
  display: inline-block;
}

.ts-category-list li a {
  font-size: 13px;
  color: #232323;
  background: #e9e9e9;
  display: block;
  line-height: 22px;
  padding: 2px 11px;
  margin-left: 6px;
  border-radius: 2px;
  -webkit-border-radius: 2px;
  -ms-border-radius: 2px;
}

.ts-category-list li a.ts-blue-bg {
  background: #007dff;
  color: #fff;
}

/*------------------------- pagination --------------*/
.pagination {
  display: block;
}

.pagination li {
  display: inline-block;
}

.pagination li a {
  display: block;
  width: 30px;
  height: 30px;
  text-align: center;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -ms-border-radius: 50%;
  line-height: 31px;
  font-size: 14px;
  color: #888888;
}

.pagination li.active a, .pagination li:hover a {
  background: #d72924;
  color: #fff;
}

.ts-pagination-1 {
  margin-top: 30px;
}

.ts-pagination-1 .pagination li a:hover {
  background: #6cba40;
}

.ts-pagination-1 .pagination li.active a {
  background: #6cba40;
}

/*-------- vertion css -----*/
.post-style-1 {
  position: relative;
  margin: 0;
}

.post-style-1 .p-1 {
  padding: 0 2px !important;
}

.post-style-1 .item {
  min-height: 500px;
}

.post-style-1.ts-overlay-style .overlay-post-content .post-title {
  margin-bottom: 5px;
}

.post-box-style .ts-grid-box {
  -webkit-box-shadow: none;
  box-shadow: none;
  background: transparent;
}

.post-box-style .ts-grid-content .post-content {
  background: transparent;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.post-box-style .ts-grid-content .item:last-of-type {
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

.post-box-style .ts-grid-content .item:last-of-type .post-content {
  border-bottom: none;
}

.post-box-style .ts-overlay-style .overlay-post-content .post-meta-info li.active {
  color: #6cba40;
}

/*---------- ts-title-item -------------*/
.ts-title-item {
  position: relative;
  margin-bottom: 30px;
}

.ts-title-item .view-all-link {
  position: relative;
  top: -8px;
}

.ts-title-item.title-item-1 .ts-title:before {
  background: #005689;
}

.ts-title-item.title-item-1 .ts-title:after {
  display: none;
}

.ts-title-item.white .ts-title {
  color: #fff;
}

.ts-cat-title {
  margin-bottom: 0;
  line-height: 0;
}

.ts-cat-title span {
  position: relative;
  font-size: 14px;
  font-weight: 400;
  background: #6cba40;
  color: #fff;
  line-height: 26px;
  padding: 0 5px;
  padding: 2px 15px;
  display: inline-block;
  z-index: 1;
}

.ts-cat-title span:before {
  position: absolute;
  width: 20px;
  height: 100%;
  content: '';
  background: #fff;
  right: -20px;
  top: 0;
}

.ts-cat-title:after {
  position: absolute;
  right: 0;
  bottom: 13px;
  width: 100%;
  height: 1px;
  background: #84be38;
  content: '';
  z-index: 0;
}

.section-bg .ts-cat-title span:before {
  background: #f7f7f7;
}

.ts-grid-item {
  padding: 0;
  background: transparent;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.ts-grid-item .overlay-post-content .post-title a:hover {
  color: #fff;
}

.ts-grid-item.widgets-populer-post {
  padding: 0;
}

.ts-grid-item.widgets-populer-post .post-content.media {
  padding-bottom: 17px;
  margin-bottom: 17px;
  border-bottom: 1px solid #ededed;
}

.ts-grid-item.widgets-populer-post .post-content.media:last-of-type {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.ts-grid-item .ts-grid-content .item:last-of-type {
  -webkit-box-shadow: none;
  box-shadow: none;
}

.ts-grid-item .ts-grid-content .item:last-of-type .post-content {
  border-bottom: none;
}

.ts-grid-item.ts-grid-item1 .col-lg-6 .ts-post-thumb img {
  max-height: 230px;
  width: 100%;
}

.ts-grid-item .ts-overlay-style .post-meta-info li.author a:hover {
  color: #6cba40;
}

.ts-post-style-2 .ts-overlay-style .item {
  min-height: 450px;
}

.grid-style-1 .ts-post-thumb {
  margin-bottom: 15px;
}

.ts-grid-style-2 .post-cat {
  margin-left: 0;
  margin-bottom: 12px;
}

/*--------------------- food css -----*/
#featured-slider-3 .item {
  min-height: 430px;
}

#featured-slider-3 .owl-dots {
  position: absolute;
  right: 0;
  top: auto;
  z-index: 1;
  bottom: 21px;
  left: 0;
  margin: auto;
  display: block;
  text-align: center;
}

#featured-slider-3 .owl-dots .owl-dot span {
  background: #fff;
}

#featured-slider-3 .owl-dots .owl-dot.active span {
  background: #fff;
}

.ts-featured-post .item {
  position: relative;
  background-position: 50% 50%;
  -webkit-background-size: cover;
  background-size: cover;
  -webkit-backface-visibility: hidden;
}

.ts-featured-post .item:before {
  background: -webkit-gradient(linear, left top, right top, color-stop(55%, rgba(0, 0, 0, 0)), to(rgba(0, 0, 0, 0.95)));
  background: -webkit-linear-gradient(left, rgba(0, 0, 0, 0) 55%, rgba(0, 0, 0, 0.95) 100%);
  background: -o-linear-gradient(left, rgba(0, 0, 0, 0) 55%, rgba(0, 0, 0, 0.95) 100%);
  background: linear-gradient(to right, rgba(0, 0, 0, 0) 55%, rgba(0, 0, 0, 0.95) 100%);
}

.ts-featured-post .overlay-post-content {
  max-width: 520px;
  right: 0;
  top: 46%;
  bottom: 0;
  margin: auto;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  z-index: 1;
  text-align: center;
}

.ts-featured-post .overlay-post-content .post-cat-name {
  margin-bottom: 30px;
  padding-bottom: 25px;
  position: relative;
  display: block;
}

.ts-featured-post .overlay-post-content .post-cat-name:before {
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  width: 45px;
  height: 1px;
  background: #897f7a;
  margin: auto;
  content: '';
}

.ts-featured-post .overlay-post-content .post-title {
  margin-bottom: 20px;
}

.post-cat-name {
  font-size: 13px;
  font-weight: 700;
  text-transform: uppercase;
  color: #6cba40;
  margin-bottom: 10px;
  display: inline-block;
  letter-spacing: 0.65px;
}

.post-cat-name:hover {
  color: #6cba40;
}

.post-author-name {
  font-size: 12px;
  line-height: 26px;
  color: #fff;
  text-transform: uppercase;
}

.post-author-name:hover {
  color: #6cba40;
}

.post-content-box .ts-post-thumb {
  margin-bottom: 0;
}

.post-content-box .post-content {
  padding: 18px 15px;
  margin: -20px 20px  0px;
  background: #fff;
  text-align: center;
}

.featured-content-box .post-content {
  padding: 35px 35px;
  margin: -40px 40px  0px;
}

/*----- post-list-tab------*/
.post-list-tab .post-tab-list {
  padding: 20px 0 0;
}

.widgets.post-list-tab .nav-tabs li a {
  line-height: 20px;
  padding-bottom: 12px;
}

.widgets.post-list-tab img.sidebar-img {
  width: 86px;
  height: 63px;
}

.ts-title-item .ts-title {
  margin-left: 20px;
}

.ts-title-item .ts-title:before {
  background: #6cba40;
  left: -20px;
}

.ts-title-item .ts-title:after {
  position: absolute;
  right: 0;
  bottom: 10px;
  width: 100%;
  height: 1px;
  background: #e4e4e4;
  content: '';
  z-index: 0;
}

.ts-title-item .ts-title span {
  background: #fff;
  z-index: 1;
  position: relative;
  padding: 0px 20px 0 0;
}

.ts-title-item.bg-gray .ts-title span {
  background: #f7f7f7;
}

.ts-overlay-item .ts-overlay-style .item .ts-post-thumb {
  margin-bottom: 0;
}

.ts-overlay-item .ts-overlay-style .ts-video-icon:hover {
  color: #fff;
}

.post-list .post-meta-info li {
  margin-right: 18px;
}

.post-list .post-meta-info li a {
  color: #232323;
  font-weight: 500;
}

.post-list .post-content-box .post-content {
  padding: 26px 15px;
  margin: -40px 20px 0px;
}

.hot-topics-item .post-list .post-content p {
  padding-right: 20px;
}

.hot-topics-item .widgets.widgets-item.widgets-populer-post {
  padding: 0;
}

/*-----------------------------
 instragram photo 
 --------------------------*/
.instagramPhoto {
  padding: 0;
  position: relative;
}

.ts-single-inst {
  width: 100%;
  overflow: hidden;
}

.ts-single-inst img {
  width: 100%;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
}

.ts-single-inst img:hover {
  opacity: 0.7;
  -webkit-transform: scale(1.06);
  -ms-transform: scale(1.06);
  transform: scale(1.06);
}

/*----------- ---------------
       technology index css
   ----------------------------*/
.featured-section-item {
  background: #000032;
}

.ts-grid-features-box {
  padding: 60px 40px 100px;
  background: #fff;
}

.ts-grid-features-box .post-cat {
  margin-left: 0;
  margin-bottom: 20px;
}

.ts-grid-features-box .post-content .post-title a:hover {
  color: #007dff;
}

.ts-grid-features-box .post-content p {
  font-size: 15px;
  margin-bottom: 25px;
  color: #777777;
}

.ts-grid-features-box .post-meta-info li.active {
  color: #007dff;
}

#featured-slider-4 .row {
  margin: 0;
}

#featured-slider-4 .item {
  min-height: 485px;
}

#featured-slider-4 .owl-nav {
  position: absolute;
  right: auto;
  top: auto;
  left: 25px;
  bottom: 50px;
}

#featured-slider-4 .owl-nav .owl-prev:before {
  display: none;
}

#featured-slider-4 .owl-nav .owl-next i, #featured-slider-4 .owl-nav .owl-prev i {
  font-size: 16px;
}

.overlay-heighlight-blue .item .post-date-info {
  color: #fff;
}

.overlay-heighlight-blue .item:before {
  background-image: -webkit-gradient(linear, left bottom, left top, from(#40c3f6), to(rgba(43, 192, 245, 0)));
  background-image: -webkit-linear-gradient(bottom, #40c3f6 0%, rgba(43, 192, 245, 0) 100%);
  background-image: -o-linear-gradient(bottom, #40c3f6 0%, rgba(43, 192, 245, 0) 100%);
  background-image: linear-gradient(0deg, #40c3f6 0%, rgba(43, 192, 245, 0) 100%);
  opacity: .6;
}

.overlay-heighlight-green .post-date-info {
  color: #fff;
}

.overlay-heighlight-green .item:before {
  background-image: -webkit-gradient(linear, left bottom, left top, from(#0cd31f), to(rgba(12, 211, 31, 0)));
  background-image: -webkit-linear-gradient(bottom, #0cd31f 0%, rgba(12, 211, 31, 0) 100%);
  background-image: -o-linear-gradient(bottom, #0cd31f 0%, rgba(12, 211, 31, 0) 100%);
  background-image: linear-gradient(0deg, #0cd31f 0%, rgba(12, 211, 31, 0) 100%);
  opacity: .6;
}

.overlay-heighlight-red .post-date-info {
  color: #fff;
}

.overlay-heighlight-red .item:before {
  background-image: -webkit-gradient(linear, left bottom, left top, from(#f71b30), to(rgba(211, 12, 35, 0)));
  background-image: -webkit-linear-gradient(bottom, #f71b30 0%, rgba(211, 12, 35, 0) 100%);
  background-image: -o-linear-gradient(bottom, #f71b30 0%, rgba(211, 12, 35, 0) 100%);
  background-image: linear-gradient(0deg, #f71b30 0%, rgba(211, 12, 35, 0) 100%);
  opacity: .6;
}

.overlay-heighlight-blue .post-date-info {
  color: #fff;
}

.overlay-heighlight-blue .item:before {
  background-image: -webkit-gradient(linear, left bottom, left top, from(#40c3f6), to(rgba(43, 192, 245, 0)));
  background-image: -webkit-linear-gradient(bottom, #40c3f6 0%, rgba(43, 192, 245, 0) 100%);
  background-image: -o-linear-gradient(bottom, #40c3f6 0%, rgba(43, 192, 245, 0) 100%);
  background-image: linear-gradient(0deg, #40c3f6 0%, rgba(43, 192, 245, 0) 100%);
  opacity: .6;
}

.overlay-heighlight-orange .post-date-info {
  color: #fff;
}

.overlay-heighlight-orange .item:before {
  background-image: -webkit-gradient(linear, left bottom, left top, from(#f2520b), to(rgba(43, 192, 245, 0)));
  background-image: -webkit-linear-gradient(bottom, #f2520b 0%, rgba(43, 192, 245, 0) 100%);
  background-image: -o-linear-gradient(bottom, #f2520b 0%, rgba(43, 192, 245, 0) 100%);
  background-image: linear-gradient(0deg, #f2520b 0%, rgba(43, 192, 245, 0) 100%);
  opacity: .7;
}

.overlay-heighlight-blue-dark .post-date-info {
  color: #fff;
}

.overlay-heighlight-blue-dark .item:before {
  background-image: -webkit-gradient(linear, left bottom, left top, from(#007dff), to(rgba(43, 192, 245, 0)));
  background-image: -webkit-linear-gradient(bottom, #007dff 0%, rgba(43, 192, 245, 0) 100%);
  background-image: -o-linear-gradient(bottom, #007dff 0%, rgba(43, 192, 245, 0) 100%);
  background-image: linear-gradient(0deg, #007dff 0%, rgba(43, 192, 245, 0) 100%);
  opacity: .7;
}

.overlay-heighlight-blue-dark .post-meta-info li.active {
  color: #007dff !important;
}

.overlay-heighlight-yellow .post-date-info {
  color: #fff;
}

.overlay-heighlight-yellow .item:before {
  background-image: -webkit-gradient(linear, left bottom, left top, from(#b18e15), to(rgba(255, 178, 45, 0)));
  background-image: -webkit-linear-gradient(bottom, #b18e15 0%, rgba(255, 178, 45, 0) 100%);
  background-image: -o-linear-gradient(bottom, #b18e15 0%, rgba(255, 178, 45, 0) 100%);
  background-image: linear-gradient(0deg, #b18e15 0%, rgba(255, 178, 45, 0) 100%);
  opacity: .8;
}

.overlay-heighlight-yellow .post-meta-info li.active {
  color: #007dff !important;
}

.ts-heading {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e5e5e5;
}

.ts-heading .ts-title {
  margin-left: 20px;
  margin-bottom: 0;
}

.ts-heading .ts-title:before {
  left: -20px;
  background: #007dff;
}

.ts-heading .view-all-link:hover {
  color: #007dff;
}

.ts-heading.white .ts-title {
  color: #fff;
}

.ts-heading.white .view-all-link {
  position: relative;
  color: #fff;
}

.featured-post .item {
  background-position: 50% 50%;
  -webkit-background-size: cover;
  background-size: cover;
  -webkit-backface-visibility: hidden;
}

.featured-post-1 .item {
  min-height: 460px;
}

.featured-post-2 .item {
  min-height: 314px;
}

/*----------------------------
  vedio-section
  ------------------------------*/
.vedio-section {
  padding: 30px 0 10px;
  position: relative;
}

.vedio-section:before {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  content: '';
  background: rgba(0, 0, 50, 0.7);
}

.post-category-item .item {
  min-height: 330px;
}

/*-----------------------
  load-more
  -------------------------*/
.load-more {
  margin-top: 20px;
}

.load-more a {
  color: #a9a9a9;
  font-size: 14px;
}

.load-more i {
  display: block;
  font-size: 50px;
  margin-bottom: 5px;
}

.blue-dark-heighlight {
  background: #005689;
  color: #fff;
}

/*----------------------------
  footer 4 
  ------------------------*/
.ts-footer-4 {
  background: #1a2130;
  padding: 40px 0;
}

.ts-footer-4 .footer-top {
  padding: 0px 0 35px;
}

.ts-footer-4 .border-top {
  border-color: #313845;
}

.ts-footer-4 .footer-social-list li a {
  color: #fff;
}

.footer-widget {
  position: relative;
}

.footer-widget .widget-title {
  color: #fff;
  font-size: 18px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.9px;
}

.footer-widget .widget-title:before {
  display: none;
}

.footer-widget ul li {
  font-size: 14px;
  line-height: 32px;
  color: #a8a9ad;
}

.footer-widget ul li a {
  color: #a8a9ad;
}

.footer-widget ul li a:hover {
  color: #005689;
}

.footer-widget p {
  color: #f0f0f1;
  font-size: 15px;
}

.footer-widget.newsletter-widgets {
  padding-left: 50px;
  padding-top: 20px;
}

.footer-widget.newsletter-widgets h4 {
  font-size: 20px;
  font-weight: 500;
  color: #fff;
  margin-bottom: 40px;
}

.footer-widget.newsletter-widgets .newsletter-widgets-form {
  position: relative;
  margin-bottom: 30px;
}

.footer-widget.newsletter-widgets .newsletter-widgets-form .newsletter-email {
  width: 100%;
  height: 40px;
  padding: 0 100px 0 20px;
}

.footer-widget.newsletter-widgets .newsletter-widgets-form .newsletter-email::-webkit-input-placeholder {
  letter-spacing: 1.2px;
  color: #a9a9a9;
}

.footer-widget.newsletter-widgets .newsletter-widgets-form .newsletter-email:-ms-input-placeholder {
  letter-spacing: 1.2px;
  color: #a9a9a9;
}

.footer-widget.newsletter-widgets .newsletter-widgets-form .newsletter-email::-ms-input-placeholder {
  letter-spacing: 1.2px;
  color: #a9a9a9;
}

.footer-widget.newsletter-widgets .newsletter-widgets-form .newsletter-email::placeholder {
  letter-spacing: 1.2px;
  color: #a9a9a9;
}

.footer-widget.newsletter-widgets .newsletter-widgets-form .btn {
  position: absolute;
  right: 0;
  top: 0;
  height: 40px;
  padding: 10px 37px;
  background: #ffaf31;
}

.footer-widget.newsletter-widgets .newsletter-widgets-form .btn:hover {
  background: #232323;
}

.footer-widget.newsletter-widgets .newsletter-widgets-form p {
  color: #8b9196;
}

.ts-footer-5 .footer-widget.border-right:after {
  position: absolute;
  right: -10px;
  top: 0;
  width: 1px;
  height: 150px;
  bottom: 0;
  margin: auto;
  content: '';
  background: #454e56;
}

.ts-footer-5 .footer-widget .footer-logo {
  padding: 20px 0;
}

.ts-footer-5 .footer-widget .footer-social-list li a:hover {
  color: #ffaf31;
}

.ts-footer-5 .footer-widget .footer-social-list li:first-child a {
  margin-left: 0;
}

.ts-footer-5 .footer-widget p {
  padding-right: 50px;
}

.copyright-section {
  background: #101727;
}

.copyright-section .footer-menu {
  margin-bottom: 0;
}

.link-img {
  display: block;
  width: 100%;
  min-height: 100px;
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
}

.img-height-310 {
  min-height: 324px;
}

/*--------------- crypto css --------------*/
.header-box-right {
  background: #10181f;
}

.header-box-right .navigation {
  height: 85px;
  background: #ffaf31;
  position: relative;
}

.header-box-right .navigation:before {
  position: absolute;
  right: -100%;
  top: 0;
  width: 100%;
  content: '';
  height: 100%;
  background: #ffaf31;
}

.header-box-right .nav-menu > li > a {
  height: 85px;
  line-height: 45px;
}

.header-box-right .nav-menu > li > a:before {
  position: absolute;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -ms-border-radius: 50%;
  background: #fff;
  content: '';
  left: 0;
  right: 0;
  margin: auto;
  bottom: 0px;
  opacity: 0;
  visibility: hidden;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
}

.header-box-right .nav-menu > li > a:hover {
  background: transparent;
  color: #fff;
}

.header-box-right .nav-menu > li > a:hover:before {
  opacity: 1;
  visibility: visible;
  bottom: 20px;
}

.header-box-right .nav-menu > li.active > a {
  background: transparent;
  color: #fff;
}

.header-box-right .nav-menu > li.active > a:before {
  opacity: 1;
  visibility: visible;
  bottom: 20px;
}

.header-box-right .nav-menu > li .megamenu-list li a:hover {
  color: #ffaf31;
}

.header-box-right .nav-menu > li .nav-dropdown li a:hover {
  color: #ffaf31;
}

.header-box-right .nav-menu > li:first-child a {
  padding-left: 35px;
}

.header-box-right .right-menu li .nav-search {
  height: 85px;
}

.header-box-right .right-menu li .nav-search .nav-search-button {
  line-height: 88px;
  text-align: right;
  background: transparent;
  color: #232323;
}

.top-bar-item .ts-date {
  color: #fff;
}

.top-bar-item .ts-top-nav li a {
  color: #fff;
}

.category-post-style1 .post-content .post-cat {
  margin-left: 0;
  margin-bottom: 15px;
}

.category-post-style1 .post-content p {
  font-size: 15px;
  color: #232323;
}

/*-------------- -------------
breakink-news-section
----------------------------------*/
.breakink-news-section {
  border-bottom: 1px solid #e5e5e5;
}

.breakink-news-section .ts-breaking-news {
  padding: 7px 20px 1px 0;
}

.breakink-news-section .ts-breaking-news .breaking-news-content {
  width: 85%;
}

.breakink-news-section #breaking_slider .owl-nav {
  top: -7px;
}

.breakink-news-section #breaking_slider .owl-nav .owl-prev, .breakink-news-section #breaking_slider .owl-nav .owl-next {
  width: 34px;
  margin-left: 0;
  height: 38px;
  font-size: 24px;
  border-left: 1px solid #e5e5e5;
  background: transparent;
}

.breakink-news-section #breaking_slider .owl-nav .owl-prev:hover, .breakink-news-section #breaking_slider .owl-nav .owl-next:hover {
  color: #ffaf31;
}

.breakink-news-section #breaking_slider .owl-nav .owl-next {
  border-right: 1px solid #e5e5e5;
}

.featured-post-style .item {
  min-height: 498px;
}

.featured-post-style .post-cat {
  margin-left: 0;
  margin-bottom: 19px;
}

.featured-post-style .overlay-post-content .post-title {
  margin-bottom: 15px;
}

.ts-grid-style-3 .ts-overlay-style {
  margin-bottom: 4px;
}

.ts-grid-style-3 .ts-overlay-style .item {
  min-height: 247px;
}

.ts-grid-style-3 .ts-overlay-style .item .post-cat {
  margin-left: 0;
  margin-bottom: 13px;
}

.ts-grid-style-3.p-1 {
  padding-top: 0 !important;
}

.currency-list-items {
  padding: 18px;
  position: relative;
  overflow: hidden;
  padding-right: 80px;
  display: block;
}

.currency-list-items h4 {
  font-size: 14px;
  font-weight: 700;
  color: #fff;
  text-transform: uppercase;
  letter-spacing: 0.7px;
}

.currency-list-items span {
  font-size: 11px;
  text-transform: uppercase;
  line-height: 15px;
  color: #efefef;
  display: block;
}

.currency-list-items .currency-right-content {
  position: absolute;
  right: 15px;
  bottom: 0;
  margin: auto;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  z-index: 1;
}

.currency-list-items img {
  position: absolute;
  right: 29px;
  bottom: -15px;
  opacity: .8;
}

.currency-list-items.currency-blue-dark-heighlight {
  background-image: -webkit-gradient(linear, left top, right top, from(#242279), to(#6663ae));
  background-image: -webkit-linear-gradient(left, #242279, #6663ae);
  background-image: -o-linear-gradient(left, #242279, #6663ae);
  background-image: linear-gradient(to right, #242279, #6663ae);
}

.currency-list-items.currency-yellow-heighlight {
  background-image: -webkit-gradient(linear, left top, right top, from(#ff8f00), to(#ffbd00));
  background-image: -webkit-linear-gradient(left, #ff8f00, #ffbd00);
  background-image: -o-linear-gradient(left, #ff8f00, #ffbd00);
  background-image: linear-gradient(to right, #ff8f00, #ffbd00);
}

.currency-list-items.currency-gray-heighlight {
  background-image: -webkit-gradient(linear, left top, right top, from(#556685), to(#98a2b5));
  background-image: -webkit-linear-gradient(left, #556685, #98a2b5);
  background-image: -o-linear-gradient(left, #556685, #98a2b5);
  background-image: linear-gradient(to right, #556685, #98a2b5);
}

.currency-list-items.currency-blue-heighlight {
  background-image: -webkit-gradient(linear, left top, right top, from(#0d21b6), to(#2a6cd4));
  background-image: -webkit-linear-gradient(left, #0d21b6, #2a6cd4);
  background-image: -o-linear-gradient(left, #0d21b6, #2a6cd4);
  background-image: linear-gradient(to right, #0d21b6, #2a6cd4);
}

.currency-list-items.currency-green-heighlight {
  background-image: -webkit-gradient(linear, left top, right top, from(#63a20a), to(#a2c821));
  background-image: -webkit-linear-gradient(left, #63a20a, #a2c821);
  background-image: -o-linear-gradient(left, #63a20a, #a2c821);
  background-image: linear-gradient(to right, #63a20a, #a2c821);
}

.bg-dark-item {
  background: #121524;
}

.bg-dark-item .ts-grid-box {
  background: transparent;
}

.bg-dark-item .nav-tabs {
  border-bottom: none;
  margin-bottom: 8px;
}

.bg-dark-item .nav-tabs li a {
  color: #fff;
}

.bg-dark-item .nav-tabs li a:hover {
  color: #fff;
}

.bg-dark-item .nav-tabs li a.active {
  background: transparent;
}

.bg-dark-item .nav-tabs li a.active:before {
  opacity: 0;
}

.bg-dark-item .post-title a {
  color: #fff;
}

.bg-dark-item .post-tag a {
  color: #ffaf31;
}

.bg-dark-item .post-tab-list .post-content.media {
  border-bottom: 1px solid #2b2b2b;
}

.bg-dark-item .post-tab-list .post-content.media:last-of-type {
  border-bottom: none;
}

/*--------------------------
navbar-standerd
---------------------------*/
.navbar-standerd {
  background: #fff;
  border-top: 1px solid #ddd;
  -webkit-box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.08);
  box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.08);
}

.navbar-standerd .navigation {
  height: 80px;
}

.navbar-standerd .navigation .nav-brand {
  padding: 0;
}

.navbar-standerd .nav-menu > li > a {
  height: 80px;
  line-height: 42px;
}

.navbar-standerd .nav-menu > li > a:hover {
  background: #fff;
}

.navbar-standerd .nav-menu > li.active > a {
  color: #d72924;
  background: #fff;
}

.navbar-standerd .right-menu li a {
  border-left: none;
  position: relative;
  color: #232323;
  height: 80px;
  line-height: 82px;
}

.navbar-standerd .right-menu li a:before {
  position: absolute;
  right: 0;
  top: 0;
  width: 1px;
  height: 20px;
  content: '';
  bottom: 0;
  margin: auto;
  background: #232323;
}

.navbar-standerd .right-menu li .nav-search .nav-search-button {
  background: transparent;
  color: #232323;
  line-height: 84px;
}

.navbar-standerd.nav-item .navigation {
  height: 60px;
}

.navbar-standerd.nav-item .nav-menu > li > a {
  height: 60px;
  line-height: 19px;
  position: relative;
}

.navbar-standerd.nav-item .nav-menu > li > a:before {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 2px;
  background: #d72924;
  content: '';
  opacity: 0;
  visibility: hidden;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
}

.navbar-standerd.nav-item .nav-menu > li.active > a:before {
  opacity: 1;
  visibility: visible;
}

.navbar-standerd.nav-item .right-menu li a {
  height: 60px;
  line-height: 61px;
  color: #888888;
}

.navbar-standerd.nav-item .right-menu li a:before {
  background: #888888;
}

.navbar-standerd.nav-item .right-menu li .nav-search .nav-search-button {
  line-height: 62px;
  color: #888888;
}

.navbar-standerd.nav-bar-dark {
  background: #272727;
  border-top: none;
}

.navbar-standerd.nav-bar-dark .navigation {
  background: #272727;
}

.navbar-standerd.nav-bar-dark .navigation .nav-menu > li > a {
  color: #fff;
}

.navbar-standerd.nav-bar-dark .navigation .nav-menu > li > a:hover {
  background: #d72924;
}

.navbar-standerd.nav-bar-dark .navigation .nav-menu > li.active > a {
  background: #d72924;
}

.navbar-standerd.nav-bar-dark .navigation .right-menu li a {
  color: #fff;
}

.navbar-standerd.nav-bar-dark .navigation .right-menu li a:before {
  background: #5d5d5d;
}

.navbar-standerd.nav-bar-dark .navigation .right-menu li .nav-search .nav-search-button {
  color: #fff;
}

.post-style-3 .featured-post.ts-overlay-style .item {
  min-height: 500px;
}

.post-style-3 .post-cat {
  margin-left: 20px;
}

.post-style-3 .post-title.large {
  margin-bottom: 15px;
  font-weight: 500;
}

.height-310 {
  min-height: 305px !important;
}

.height-190 {
  height: 190px !important;
}

.post-style-4 .ts-overlay-style {
  margin-bottom: 4px;
}

.ts-heading-item .ts-title {
  margin-left: 0;
}

.ts-heading-item .ts-title span {
  background: #fff;
  padding-right: 10px;
  position: relative;
  z-index: 1;
}

.ts-heading-item .ts-title:before {
  display: none;
}

.ts-heading-item .ts-title:after {
  position: absolute;
  right: 0;
  top: 13px;
  width: 100%;
  height: 1px;
  background: #616161;
  content: '';
}

.ts-grid-box.bg-dark-item .ts-heading-item .ts-title span {
  background: #121212;
  color: #fff;
}

.ts-grid-box.bg-dark-item .ts-heading-item .ts-title:after {
  background: #252525;
}

.ts-grid-box.bg-dark-item .post-content {
  border-color: #252525;
}

.ts-grid-box.bg-dark-item .post-meta-info li.active a {
  color: #d72924;
}

.ts-grid-item-2 .post-cat {
  margin-left: 0;
  margin-bottom: 16px;
}

.ts-grid-item-2 .ts-post-thumb {
  margin-bottom: 0;
}

.ts-grid-item-2 .item .post-content {
  padding: 18px 0;
  border-bottom: 1px solid #e8e8e8;
}

.ts-grid-item-2 .item:last-of-type .post-content {
  border-bottom: none;
}

.right-sidebar-1 .widgets.widgets-item .widget-title {
  margin-left: 0;
}

.right-sidebar-1 .widgets.widgets-item .widget-title span {
  background: #fff;
  padding-right: 10px;
  position: relative;
}

.right-sidebar-1 .widgets.widgets-item .widget-title:before {
  left: 0;
  background: #d72924;
  width: 100%;
  height: 1px;
  bottom: 0;
  margin: auto;
}

.ts-grid-box.widgets.border {
  -webkit-box-shadow: none;
  box-shadow: none;
  border: 1px solid #e3e3e3;
}

/*------------------ home default 5------------*/
#featured-slider-5 .item {
  min-height: 500px;
}

#featured-slider-5 .item:hover:after {
  display: none;
}

#featured-slider-5 .item:before {
  background: rgba(0, 0, 0, 0.3);
}

#featured-slider-5 .overlay-post-content {
  z-index: 1;
  top: 50%;
  -webkit-transform: translateY(-31%);
  -ms-transform: translateY(-31%);
  transform: translateY(-31%);
  max-width: 570px;
}

#featured-slider-5 .overlay-post-content .post-content {
  padding: 0 40px;
}

#featured-slider-5 .overlay-post-content .post-title.lg {
  margin-bottom: 20px;
}

#featured-slider-5 .post-cat {
  margin-left: 0;
  margin-bottom: 15px;
}

#featured-slider-5 .owl-dots {
  top: 50%;
  right: 30px;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
}

#featured-slider-5 .owl-dots .owl-dot {
  display: block;
  margin: 0;
  top: 0;
  line-height: 16px;
}

#featured-slider-5 .owl-dots .owl-dot span {
  background: #fff;
}

#featured-slider-5 .owl-dots .owl-dot.active span {
  margin: 0;
}

/*------------------------------
    404 
------------------------------*/
.error-page {
  padding: 80px 30px;
}

.error-page .error-code h2 {
  font-size: 220px;
  font-weight: 500;
  color: #222222;
  line-height: 220px;
}

.error-page .error-message h3 {
  font-size: 40px;
  margin-bottom: 16px;
}

.error-page .error-body h4 {
  font-size: 22px;
  font-weight: 400;
  margin-bottom: 34px;
}

.error-page .error-body a {
  background: #d72924;
}

.error-page .error-body a.btn {
  padding: 17px 37px;
}

.error-page .error-body a.btn:hover {
  background: #c2211c;
}

/*--------------------------------
          sport index css 
------------------------------------*/
.top-bar.transparent {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  background: transparent;
  z-index: 2;
}

.top-bar.transparent #breaking_slider .owl-nav {
  top: 0;
  right: -46px;
}

.top-bar.transparent #breaking_slider .owl-nav .owl-prev,
.top-bar.transparent #breaking_slider .owl-nav .owl-next {
  color: #fff;
  background: #111c20;
}

.top-bar.transparent .ts-breaking-news {
  background: transparent;
  padding: 8px 20px 8px 0;
}

.top-bar.transparent .ts-breaking-news .breaking-news-content {
  width: 85%;
}

.top-bar.transparent .ts-breaking-news p a {
  color: #fff;
}

.header-box-transprent {
  position: absolute;
  right: 0;
  left: 0;
  width: 100%;
  top: 40px;
  height: auto;
  z-index: 2;
}

.header-box-transprent .ts-main-menu {
  height: 65px;
  background: rgba(0, 0, 0, 0.6);
}

.header-box-transprent .ts-main-menu .nav-header {
  width: auto;
  height: 100%;
}

.header-box-transprent .ts-main-menu .nav-header .nav-brand {
  line-height: 64px;
  padding: 0 18px;
}

.header-box-transprent .ts-main-menu .nav-menu > li > a {
  height: 65px;
  padding: 24px 20px;
  font-weight: 500;
  color: #fff;
}

.header-box-transprent .ts-main-menu .nav-menu > li > a:hover {
  background: transparent;
}

.header-box-transprent .ts-main-menu .nav-menu > li > a .submenu-indicator:before {
  content: '\f107';
}

.header-box-transprent .ts-main-menu .nav-menu > li.active a {
  background: transparent;
}

.header-box-transprent .ts-main-menu .nav-menu > li:first-child a {
  padding-left: 30px;
}

.header-box-transprent .ts-main-menu .right-menu li a {
  height: 65px;
  line-height: 65px;
  width: 65px;
  color: #fff;
  border-left: none;
  border-right: 1px solid #ff3639;
}

.header-box-transprent .ts-main-menu .right-menu li .nav-search {
  height: 65px;
  width: 65px;
  border-left: 1px solid #bb1619;
}

.header-box-transprent .ts-main-menu .right-menu li .nav-search .nav-search-button {
  line-height: 67px;
  width: 100%;
  background: transparent;
}

/*---------------------
     hero section 
-----------------*/
.featured-table {
  width: 100%;
  height: 100%;
  display: table;
}

.featured-table .table-cell {
  display: table-cell;
  width: 100%;
  height: 100%;
  vertical-align: middle;
}

.hero-area {
  padding: 0;
  position: relative;
}

.hero-area:before {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 130px;
  content: '';
  background-image: -webkit-gradient(linear, left top, left bottom, from(0), color-stop(0%, white), to(rgba(255, 255, 255, 0)));
  background-image: -webkit-linear-gradient(0, white 0%, rgba(255, 255, 255, 0) 100%);
  background-image: -o-linear-gradient(0, white 0%, rgba(255, 255, 255, 0) 100%);
  background-image: linear-gradient(0, white 0%, rgba(255, 255, 255, 0) 100%);
  z-index: 1;
}

.featured-slider-item {
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  position: relative;
  height: 960px;
}

.featured-slider-item::before {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  content: '';
  background: rgba(0, 0, 0, 0.2);
}

.hero-content {
  position: relative;
  padding: 20px;
  margin-top: -180px;
}

.hero-content .post-cat {
  margin-bottom: 12px;
  margin-left: 0;
}

.hero-content h2 {
  font-size: 42px;
  font-weight: 700;
  color: #fff;
  margin-bottom: 22px;
}

.hero-content p {
  font-size: 18px;
  line-height: 24px;
  color: #fff;
}

.featured-bottom-post {
  margin-top: -260px;
  position: relative;
  z-index: 1;
}

/*--------------
tab-menu-item
-----------*/
.tab-menu-item li a {
  font-size: 15px;
  font-weight: 400;
  color: #5a5c66;
  padding: 0 15px;
}

.tab-menu-item li a.active {
  color: #fff;
}

.tab-menu-item li:last-child a {
  padding-right: 0;
}

.ts-tabs-content .tab-pane.fade.active {
  opacity: 1;
}

.video-item {
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
  height: 450px;
  position: relative;
  overflow: hidden;
}

.video-item .post-video {
  position: absolute;
  top: 46%;
  margin: auto;
  text-align: center;
  -webkit-transform: translateX(50%);
  -ms-transform: translateX(50%);
  transform: translateX(50%);
  left: 0;
  right: 0;
  z-index: 2;
  margin: auto;
}

.video-item .post-video:before {
  display: none;
}

.video-item .post-video .ts-play-btn {
  z-index: 1;
  border: none;
}

.video-tab-list {
  height: 450px;
  padding: 30px 5px 30px 30px;
  overflow: hidden;
}

.video-tab-list img.sidebar-img {
  width: 100px;
  height: auto;
}

.video-tab-list .post-tab-list {
  padding: 0;
}

.video-tab-list .post-tab-list li {
  margin-bottom: 15px;
}

.video-tab-list .post-tab-list li .post-title {
  color: #fff;
}

.video-tab-list .post-tab-list li:last-child {
  margin-bottom: 0;
}

.fade.in {
  opacity: 1;
}

.post-style-item4 {
  padding-bottom: 0;
}

.post-style-item4 .ts-post-thumb {
  margin-bottom: 0;
}

.post-style-item4 .item:before {
  background: -webkit-gradient(linear, left top, left bottom, color-stop(55%, rgba(0, 0, 0, 0)), to(rgba(0, 0, 0, 0.75)));
  background: -webkit-linear-gradient(top, rgba(0, 0, 0, 0) 55%, rgba(0, 0, 0, 0.75) 100%);
  background: -o-linear-gradient(top, rgba(0, 0, 0, 0) 55%, rgba(0, 0, 0, 0.75) 100%);
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 55%, rgba(0, 0, 0, 0.75) 100%);
}

#hero-slider .owl-nav {
  position: relative;
  right: auto;
  top: 0%;
  left: 0%;
  z-index: 3;
}

#hero-slider .owl-prev {
  font-size: 20px;
  text-transform: uppercase;
  padding: 20px;
}

#hero-slider .owl-prev:before {
  display: none;
}

#hero-slider .owl-next {
  font-size: 20px;
  text-transform: uppercase;
  padding: 20px;
  margin-left: 80px;
}

#hero-slider .owl-dots {
  counter-reset: slides-num;
  margin-top: 15px;
  right: auto;
  left: 0;
}

#hero-slider .owl-dots:after {
  content: "0" counter(slides-num);
  display: inline-block;
  font-size: 12px;
  font-weight: 700;
  vertical-align: middle;
  padding-left: 20px;
  margin-top: -10px;
  font-weight: 400;
  color: #fff;
}

#hero-slider .owl-dot {
  display: inline-block;
  counter-increment: slides-num;
  /* Increment counter */
  margin-right: 5px;
}

#hero-slider .owl-dot span {
  display: none;
}

#hero-slider .owl-dot.active:before {
  content: "0" counter(slides-num) " /  ";
  /* Use the same counter to get current item. */
  display: inline-block;
  vertical-align: middle;
  font-size: 20px;
  position: absolute;
  left: 0;
  top: 0;
  color: #d72924;
}

#hero-slider .owl-item.active .hero-content a.post-cat {
  -webkit-animation-name: animLeft;
  animation-name: animLeft;
  -webkit-animation: animLeft 1s ease 100ms both;
  animation: animLeft 1s ease 100ms both;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-animation-delay: 500ms;
  animation-delay: 500ms;
}

#hero-slider .owl-item.active .hero-content h2 {
  -webkit-animation-name: animLeft;
  animation-name: animLeft;
  -webkit-animation: animLeft 1s ease 100ms both;
  animation: animLeft 1s ease 100ms both;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-animation-delay: 800ms;
  animation-delay: 800ms;
}

#hero-slider .owl-item.active .hero-content p {
  -webkit-animation-name: animLeft;
  animation-name: animLeft;
  -webkit-animation: animLeft 1s ease 100ms both;
  animation: animLeft 1s ease 100ms both;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-animation-delay: 1200ms;
  animation-delay: 1200ms;
}

@-webkit-keyframes animLeft {
  from {
    opacity: 0;
    -webkit-transform: translate3d(40%, 0, 0);
    transform: translate3d(40%, 0, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}

.slider-dot-item {
  position: absolute;
  left: 0;
  width: 100%;
  top: 59.6%;
  right: 0;
  margin: auto;
  padding-left: 90px;
}

.slider-arrow-item {
  position: absolute;
  left: 0;
  width: 100%;
  top: 55.8%;
  right: 0;
  margin: auto;
}

.slider-arrow-item .owl-nav .owl-next i, .slider-arrow-item .owl-nav .owl-prev i {
  font-size: 16px;
  color: #fff;
}

.mCSB_scrollTools .mCSB_draggerRail {
  width: 5px;
  background-color: #414450;
}

/*----------------------------
    travel index css 
---------------------------*/
.header-transprent {
  top: 25px;
}

.header-transprent .ts-main-menu {
  background: transparent;
}

.header-transprent .ts-main-menu .nav-menu > li > a {
  font-weight: 400;
}

.header-transprent .ts-main-menu .nav-menu > li.header-search .header-search-form {
  position: absolute;
  left: 0;
  width: 100%;
  background: #fff;
  display: none;
}

.featured-area:before {
  display: none;
}

.featured-area .item {
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  height: 900px;
}

.featured-area .item .hero-content {
  margin-top: 0;
}

.featured-area .item .hero-content h2 {
  font-weight: 500;
  margin-bottom: 60px;
  font-size: 48px;
}

.featured-area .item .hero-content .featurd-video-icon {
  font-size: 80px;
  color: #fff;
}

@media (min-width: 1400px) {
  .featured-area .item {
    height: 1050px;
  }
}

.grid-style-2 .ts-overlay-style .item {
  min-height: 456px;
}

/*------------------ accordion post style ------------*/
.accordion-post-style .card {
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
}

.accordion-post-style .card:before {
  content: " ";
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  z-index: 1;
  bottom: 0;
  left: 0;
  background: -webkit-gradient(linear, left top, left bottom, color-stop(55%, rgba(0, 0, 0, 0)), to(rgba(24, 106, 179, 0.7)));
  background: -webkit-linear-gradient(top, rgba(0, 0, 0, 0) 55%, rgba(24, 106, 179, 0.7) 100%);
  background: -o-linear-gradient(top, rgba(0, 0, 0, 0) 55%, rgba(24, 106, 179, 0.7) 100%);
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 55%, rgba(24, 106, 179, 0.7) 100%);
}

.accordion-post-style .card .space {
  height: 125px;
  text-align: center;
  display: block;
}

.accordion-post-style .card .btn {
  position: relative;
  z-index: 1;
  padding: 8px 20px;
  border-radius: 36px;
  -webkit-border-radius: 36px;
  -ms-border-radius: 36px;
  opacity: 0;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  -webkit-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
}

.accordion-post-style .card .btn:hover {
  color: #fff;
}

.accordion-post-style .card .card-header {
  padding: 0;
}

.accordion-post-style .card .card-header a {
  display: block;
  padding: 20px 20px 15px 55px;
  position: relative;
  color: #fff;
  z-index: 1;
}

.accordion-post-style .card .card-header a .post-title {
  color: #fff;
  margin-bottom: 0;
  font-weight: 500;
}

.accordion-post-style .card .card-header a i {
  position: absolute;
  left: 0;
  top: 0;
  font-size: 35px;
  left: 20px;
  top: 20px;
}

.accordion-post-style .card:hover .btn {
  opacity: 1;
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
}

.post-overflow-style .ts-title:after {
  display: none;
}

.post-overflow-style .ts-post-thumb {
  overflow: visible;
  margin-right: -230px;
  width: auto;
  z-index: 2;
}

.post-overflow-style .post-content-item {
  background: #f7f7f7;
  padding: 70px 20px 55px 200px;
  margin-top: -55px;
  margin-left: 30px;
}

.post-overflow-style .post-content-item .post-title {
  margin-bottom: 24px;
}

.post-overflow-style .post-content-item p {
  margin-bottom: 28px;
}

.watching-section {
  padding: 60px 0 50px;
}

.watching-section .post-title a {
  color: #fff;
}

.watching-section .post-title a:hover {
  color: #fff;
}

.border-style .post-cat {
  margin-left: 0;
  top: -12px;
}

.border-style.ts-grid-box.ts-grid-content .post-content {
  -webkit-box-shadow: none;
  box-shadow: none;
  padding-top: 25px;
  border: 1px solid #e6e6e6;
  border-top: none;
}

.border-style.ts-grid-box.ts-grid-content .ts-post-thumb {
  margin-bottom: 0;
}

/*-----------------
featured tab personal blog 
--------------*/
.featured-tab-item .featured-tab-post {
  margin: 0;
  border-bottom: none;
}

.featured-tab-item .featured-tab-post .post-content {
  padding: 26px 30px 10px;
  background: #fff;
}

.featured-tab-item .featured-tab-post > li {
  padding: 0;
  border-right: 1px solid #e6e6e6;
}

.featured-tab-item .featured-tab-post > li:last-child {
  border-right: none;
}

.featured-tab-item .featured-tab-post > li .active:before {
  position: absolute;
  right: 0;
  top: -15px;
  width: 100%;
  height: 15px;
  content: '';
}

.featured-tab-item .featured-tab-post > li .active .post-title,
.featured-tab-item .featured-tab-post > li .active .post-meta-info li,
.featured-tab-item .featured-tab-post > li .active .post-meta-info li.cat-name {
  color: #fff;
}

.view-link-btn {
  position: absolute;
  left: 0;
  top: 50%;
  right: 0;
  margin: auto;
  text-align: center;
  margin: auto;
  -webkit-transform: translateY(-20%);
  -ms-transform: translateY(-20%);
  transform: translateY(-20%);
}

.view-link-btn span {
  background: #fff;
  height: 48px;
  line-height: 48px;
  padding: 2px 20px;
  min-width: 140px;
  display: inline-block;
  text-transform: uppercase;
  font-size: 11px;
  color: #222222;
  font-weight: 500;
}

.blog-post-slider-item .ts-grid-box {
  padding: 90px 230px 90px 30px;
}

.blog-post-slider-item .ts-post-thumb {
  overflow: visible;
  margin-left: -244px;
  width: auto;
  z-index: 2;
  margin-bottom: 0;
}

.blog-post-slider-item .ts-post-thumb img {
  max-height: 310px;
  width: 100%;
}

.blog-post-slider-item .owl-nav {
  position: absolute;
  right: auto;
  top: auto;
  left: 14px;
  bottom: 41px;
}

.read-more-btn {
  font-size: 11px;
  font-weight: 500;
  color: #888888;
  text-transform: uppercase;
  border: 1px solid #ddd;
  display: inline-block;
  padding: 4px 20px;
}

.older-post-btn .read-more-btn {
  background: #fff;
  color: #232323;
  border: none;
}

.author-box-widget {
  padding: 0;
}

.author-box-widget .ts-post-thumb {
  margin-bottom: 0;
}

.author-box-widget .post-content {
  padding: 20px;
  text-align: center;
}

.author-box-widget .post-content p {
  font-size: 14px;
  color: #585858;
}

.author-box-widget .authors-social a {
  color: #8a8a8a;
  margin: 0 8px;
}

.ts-post-style-3 {
  margin-left: -2px;
  margin-right: -2px;
}

.ts-post-style-3 .ts-post-thumb {
  margin-bottom: 0;
}

.ts-post-style-3 .p-1 {
  padding: 2px !important;
}

.ts-post-style-3 .post-content .post-cat {
  margin-left: 0;
  margin-bottom: 10px;
}

.sidebar-2 .widgets-populer-post .post-content.media {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ddd;
}

.sidebar-2 .widgets-populer-post .post-content.media:last-of-type {
  margin-bottom: 0;
  border-bottom: none;
}

.ts-grid-content-1.ts-blue-light-heighlight {
  background: #007dff;
}

.ts-grid-content-1.ts-blue-light-heighlight p,
.ts-grid-content-1.ts-blue-light-heighlight .post-date-info,
.ts-grid-content-1.ts-blue-light-heighlight .post-title a {
  color: #fff;
}

.ts-grid-content-1.ts-blue-light-heighlight .post-cat {
  color: #007dff;
}

.img-height-520 {
  min-height: 522px;
}

.ts-post-style-4 .ts-grid-style-3 .item {
  min-height: 235px;
  margin-bottom: 10px;
}

.border-top1 {
  border-color: #cfd2da;
}

/*------------------------------
  New Update - Contact, to-top, login, signup
------------------------------*/
/* Contact Page */
.contact-box h3 {
  font-size: 28px;
}

.contact-box h4 {
  color: #000;
}

/* Login page */
.login-or {
  position: relative;
  font-size: 18px;
  color: #aaa;
  margin-top: 10px;
  margin-bottom: 10px;
  padding-top: 10px;
  padding-bottom: 10px;
}

.login-or .span-or {
  display: block;
  position: absolute;
  left: 50%;
  top: 15px;
  margin-left: -25px;
  background-color: #fff;
  width: 50px;
  text-align: center;
}

.btn.btn-primary.btn-fb {
  background: #3b5999;
}

/* Reg page */
.reg-page .text-muted {
  font-size: 14px;
  margin-bottom: 20px;
  display: block;
}

@media (min-width: 992px) and (max-width: 1200px) {
  .ts-breaking-news .breaking-news-content .owl-nav {
    right: -32px;
  }
  .logo img {
    width: 100%;
    padding: 0 5px;
  }
  .nav-menu > li > a {
    padding: 22px 16px;
  }
  /*----------- centerd menu and icon ----*/
  .menu-centerd .ts-main-menu .nav-menu li a {
    padding: 22px 13px;
  }
  .menu-centerd.nav-icon-item .nav-menu li a {
    padding: 22px 41px;
  }
  .header-transparent .nav-menu > li > a {
    padding: 31px 17px;
  }
  #breaking_slider .owl-nav {
    right: -32px;
  }
  .header-box-right .navigation:before {
    display: none;
  }
  .header-box-right .right-menu li .nav-search .nav-search-button {
    text-align: center;
  }
  .footer-top .footer-menu ul li a {
    margin: 0 12px;
  }
  .footer-bottom .footer-social-list li a {
    margin: 0 30px;
  }
  .top-bar.transparent #breaking_slider .owl-nav {
    top: 0;
    right: -19px;
  }
  .footer .footer-social-list li a {
    margin-left: 12px;
  }
  #post-slider1 .owl-nav {
    left: 90px;
  }
  .accordion-post-style .card .card-header a .post-title {
    font-size: 17px;
  }
  .blog-post-slider-item .ts-post-thumb {
    margin-left: -230px;
  }
  .blog-post-slider-item .ts-grid-box {
    padding: 90px 208px 90px 30px;
  }
  .posts-ad img {
    max-width: 100%;
  }
  .footer-social li {
    margin-bottom: 14px;
  }
  .header-box-transprent .ts-main-menu .nav-menu > li > a {
    padding: 24px 13px;
  }
  .ts-tranding-post .slider-indicators .post-content .post-count {
    width: 48px;
    height: 48px;
    padding: 0 12px;
  }
  .footer-social li {
    margin-bottom: 12px;
  }
  .widget-banner {
    text-align: center;
  }
  /*--------- single post ----*/
  .single-post ul li {
    margin-bottom: 14px;
    font-size: 14px;
  }
  #featured-slider-4 .owl-nav {
    bottom: 100px;
  }
  .post-title.ex-lg {
    font-size: 36px;
  }
  .post-title.lg {
    font-size: 30px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .pl-1 {
    padding-left: 15px !important;
  }
  .p-1 {
    padding: 0 15px !important;
  }
  .pr-1,
  .pr-10 {
    padding-right: 15px !important;
  }
  .pr-0 {
    padding-right: 15px !important;
  }
  .p-0 {
    padding: 0 15px !important;
  }
  .pl-0 {
    padding-left: 15px !important;
  }
  .top-social {
    text-align: center;
  }
  .md-center-item {
    text-align: center;
  }
  .posts-ad {
    text-align: center;
  }
  .posts-ad img {
    max-width: 100%;
  }
  .header-standerd .nav-toggle:before,
  .header-box-right .nav-toggle:before {
    background-color: #ffffff;
    -webkit-box-shadow: 0 0.5em 0 0 #ffffff, 0 1em 0 0 #ffffff;
    box-shadow: 0 0.5em 0 0 #ffffff, 0 1em 0 0 #ffffff;
  }
  .header-standerd .nav-menu > li > a {
    color: #232232;
  }
  .nav-icon-item .nav-menu li a i {
    text-align: left !important;
  }
  .ts-breaking-news .breaking-news-content {
    width: 100%;
  }
  .ts-breaking-news .breaking-news-content .owl-nav {
    right: 0;
    top: -27px;
  }
  .logo {
    display: none;
  }
  .mobile-logo {
    display: block;
  }
  .header-nav-item {
    padding-left: 15px;
  }
  .ts-overlay-style.ts-featured .item {
    min-height: 400px;
  }
  .post-title.lg {
    font-size: 30px;
  }
  .ts-overlay-style.ts-featured .item .post-content {
    padding: 17px;
  }
  .ts-featured,
  .posts-ad,
  .ts-overlay-style,
  .ts-grid-box:last-of-type,
  .ts-newslatter-content {
    margin-bottom: 30px;
  }
  .footer-logo {
    margin-bottom: 15px;
  }
  .footer-social li {
    margin-right: 25px;
  }
  #breaking_slider .owl-nav {
    top: -24px;
    right: 0;
  }
  .nav-menu .megamenu-panel {
    -webkit-box-shadow: none;
    box-shadow: none;
    padding: 0 15px;
  }
  .megamenu-panel .item {
    margin-bottom: 15px;
  }
  /*---------- single post style -------------*/
  .post-layout-2 .single-big-img .entry-header {
    padding: 12px 27px;
  }
  /*--- health index----*/
  .ts-grid-item .item {
    margin-bottom: 20px;
  }
  .ts-grid-item .ts-grid-content.ts-list-post-box .item {
    margin-bottom: 0;
  }
  /*------- food index css -----*/
  .nav-icon-item .navigation {
    height: 70px;
  }
  .nav-icon-item .nav-menu li a i {
    text-align: left;
  }
  .nav-icon-item .ts-main-menu .right-menu li .nav-search .nav-search-button {
    text-align: left;
    padding: 25px 45px !important;
  }
  .ts-pagination-1 {
    margin-bottom: 30px;
  }
  /*------- technology index css --------------*/
  .navigation-portrait .submenu-indicator {
    height: auto;
    top: 11px;
  }
  .header-transparent .navigation-portrait .nav-menus-wrapper {
    background: #000032;
  }
  .header-transparent .navigation-portrait .submenu-indicator {
    top: 27px;
  }
  .post-title.ex-lg {
    font-size: 30px;
  }
  .ts-grid-features-box {
    padding: 60px 40px 50px;
  }
  .left-sidebar {
    margin-bottom: 30px;
  }
  .ts-footer.ts-footer-3 .footer-social-list li a {
    margin-left: 25px;
  }
  /*--------------------------
    business index css
    ----------------*/
  .footer-widget {
    margin-bottom: 40px;
  }
  /*--------------------- crypto index css ------------*/
  .header-box-right .navigation:before {
    display: none;
  }
  .header-box-right .nav-menu > li.active {
    background: #232323;
    color: #fff;
  }
  .header-box-right .nav-menu > li > a {
    height: 60px;
  }
  .header-box-right .nav-menu > li > a:before {
    display: none;
  }
  .breakink-news-section #breaking_slider .owl-nav {
    top: -32px;
  }
  .breakink-news-section #breaking_slider .owl-nav .owl-prev,
  .breakink-news-section #breaking_slider .owl-nav .owl-next {
    height: 26px;
  }
  .ts-footer-5 .footer-logo {
    text-align: left;
  }
  .ts-footer-5 .footer-social-list {
    text-align: left !important;
  }
  .ts-footer-5 .footer-widget.newsletter-widgets {
    padding-left: 30px;
  }
  .footer-menu ul li a {
    margin: 0 10px;
  }
  /*----------- index default four -------------*/
  .navbar-standerd .navigation-portrait .nav-brand {
    line-height: 70px;
  }
  .navbar-standerd .nav-menu > li > a {
    height: 60px;
  }
  /*---------------------------- sport index ------------*/
  .header-box-transprent {
    top: 80px;
  }
  .header-box-transprent .ts-main-menu .nav-menu > li > a {
    color: #232323;
  }
  .footer-top .footer-menu.xs-center {
    text-align: center !important;
  }
  .footer-top .footer-logo {
    margin: 20px 0 30px;
  }
  .footer-bottom .footer-social-list {
    margin: 20px 0;
  }
  .footer-bottom .copy-right-text.xs-center {
    text-align: center !important;
  }
  .p-10 {
    padding: 0 15px;
  }
  /*----------------- travel index css ------------*/
  .header-transprent {
    top: 25px;
  }
  .header-transprent .nav-menu > li > a:hover {
    color: #232232;
  }
  .header-transprent .nav-menu > li > a .submenu-indicator {
    margin-top: 14px;
  }
  .header-transprent .nav-menu > li:first-child > a {
    padding-left: 15px;
  }
  .header-transprent .nav-toggle:before {
    background-color: #fff;
    border-radius: 10px;
    -webkit-box-shadow: 0 0.5em 0 0 #fff, 0 1em 0 0 #fff;
    box-shadow: 0 0.5em 0 0 #fff, 0 1em 0 0 #fff;
  }
  .header-transprent .mobile_hidden {
    display: none;
  }
  .header-transprent .nav-menu-centered .header-search {
    display: none;
  }
  .header-box-transprent .ts-main-menu .nav-menu > li:first-child a {
    padding-left: 20px;
  }
  .featured-area .item {
    height: 600px;
  }
  #post-slider1 .owl-nav {
    left: 0;
  }
  .post-overflow-style .post-content-item {
    padding: 70px 20px 20px 40px;
    margin-left: 0;
  }
  .block-wrapper-1.p-50 {
    padding: 30px 0;
    margin: 0;
  }
  .grid-style-2 .ts-overlay-style .item {
    min-height: 350px;
  }
  .accordion-post-style {
    margin-bottom: 30px;
  }
  .col-lg-3.pl-0 .accordion-post-style {
    margin-bottom: 0px;
  }
  .watching-section .item {
    margin-bottom: 20px;
  }
  .footer .copyright-text {
    margin-bottom: 20px;
    text-align: center;
  }
  section.block-wrapper.block-wrapper-1.p-50.mb-40,
  section.block-wrapper.block-wrapper-1.p-50.mt-50 {
    padding-bottom: 0;
  }
  section.block-wrapper.block-wrapper-1.p-100 {
    padding: 30px 0;
  }
  .ts-overlay-style .overlay-post-content {
    width: 100%;
  }
  .blog-post-slider-item .ts-post-thumb {
    margin-left: -230px;
  }
  .blog-post-slider-item .ts-grid-box {
    padding: 90px 208px 90px 30px;
  }
  .post-col-list-item .older-post-btn {
    margin-bottom: 40px;
  }
  .navbar-standerd.nav-bar-dark .navigation-portrait .nav-menus-wrapper {
    background: #232323;
  }
  .navbar-standerd.nav-bar-dark .navigation-portrait .nav-menu > li {
    border-top: solid 1px #444444;
  }
  .category-box-item-3 .row .col-lg-6 .item {
    margin-bottom: 20px;
  }
  .category-item .item {
    margin-bottom: 20px;
  }
  section.block-wrapper.mb-45 {
    margin-bottom: 30px;
  }
  .post-list-box.ts-list-post-box.ts-grid-content {
    margin-bottom: 20px;
  }
  .row.post-col-list-item .col-lg-6.mb-30 {
    margin-bottom: 0 !important;
  }
  #more-news-section {
    margin-bottom: 0 !important;
  }
  #featured-slider-4 .item {
    min-height: 350px;
  }
  .plr-30 {
    padding: 0 15px;
  }
  .md-center {
    text-align: center !important;
  }
  .ts-grid-item.ts-grid-item1 .col-lg-6 .ts-post-thumb img {
    max-height: inherit;
    width: 100%;
  }
  .widget-banner,
  .banner-widget {
    text-align: center;
  }
  .ts-post-overlay-style-1 .ts-overlay-style:last-of-type {
    margin-bottom: 0;
  }
  .ts-post-style-5.ts-post-overlay-style-1 .ts-overlay-style:last-of-type {
    margin-bottom: 30px;
  }
  .post-list-box.ts-list-post-box.ts-grid-content .ts-overlay-style {
    margin-bottom: 10px;
  }
  .md-mb-30,
  .ts-pagination.text-center.mb-20 {
    margin-bottom: 30px;
  }
  /*--- sidebar ----*/
  .widget-banner {
    text-align: center;
  }
  .right-sidebar-1 .widgets .category-list {
    margin-bottom: 20px;
  }
  .sidebar {
    margin-top: 20px;
  }
  .single-post.content-wrapper, .comments-form.ts-grid-box {
    padding: 30px 40px 40px;
  }
  .single-post .post-media {
    margin: 0 -40px;
  }
  .post-meta-info li {
    margin-right: 20px;
    margin-bottom: 12px;
  }
  .category-layout-3 .ts-grid-box.entry-header {
    margin-bottom: 0;
  }
  .category-layout-3 .col-lg-6.mb-30 {
    margin-bottom: 0 !important;
  }
  .ts-tranding-post .slider-indicators.carousel-indicators > li:before,
  .ts-tranding-post .slider-indicators.carousel-indicators > li:after {
    display: none;
  }
  .category-box-item-3 .col-lg-6 .item {
    margin-bottom: 30px;
  }
  .ts-grid-box.category-box-item-3,
  .ts-grid-box.category-item {
    padding-bottom: 20px;
  }
  #more-news-section {
    padding-top: 0;
  }
  /*--------- single post ----*/
  .single-post ul li {
    margin-bottom: 14px;
    font-size: 14px;
  }
  .ts-footer-5 .footer-widget.border-right:after {
    display: none;
  }
}

@media (max-width: 767px) {
  .pl-1 {
    padding-left: 15px !important;
  }
  .p-1 {
    padding: 0 15px !important;
  }
  .pr-1 {
    padding-right: 15px !important;
  }
  .pr-0 {
    padding-right: 15px !important;
  }
  .p-0 {
    padding: 0 15px !important;
  }
  .pl-0 {
    padding-left: 15px !important;
  }
  .xs-center {
    text-align: center !important;
  }
  /*--- top bar ---*/
  .top-bar .ts-temperature {
    text-align: center;
    border-right: none;
    display: block;
    padding-right: 0;
  }
  .top-bar .ts-temperature:before {
    display: none;
  }
  .top-bar .top-social {
    text-align: center;
  }
  .top-bar .ts-top-nav {
    display: block;
    text-align: center;
    padding-left: 0;
  }
  .top-bar.v3 .ts-date {
    display: block;
    padding-right: 0;
    text-align: center;
  }
  .logo {
    display: none;
  }
  .mobile-logo {
    display: block;
  }
  .header-nav-item {
    padding-left: 15px;
  }
  #breaking_slider {
    width: 100%;
  }
  #breaking_slider .owl-nav {
    position: absolute;
    right: 0;
    top: -27px;
  }
  .header-middle.v2 .logo {
    display: block;
  }
  #featured-slider-2 .item {
    min-height: 280px;
  }
  #featured-slider-2 .post-title.lg {
    font-size: 20px;
  }
  /*---- blog post ----*/
  .ts-overlay-style.ts-featured .item {
    min-height: 440px;
  }
  .post-title.lg {
    font-size: 30px;
  }
  .ts-overlay-style.ts-featured .item .post-content {
    padding: 17px;
  }
  .ts-featured,
  .posts-ad,
  .ts-overlay-style,
  .ts-grid-box:last-of-type,
  .ts-newslatter-content {
    margin-bottom: 30px;
  }
  .watch-now-featured .item .ts-video-btn {
    top: 40px;
  }
  .ts-tranding-post .slider-indicators.carousel-indicators > li {
    width: 100%;
  }
  #more-news-slider .ts-overlay-style {
    margin-bottom: 0;
  }
  .ts-grid-box {
    padding: 15px;
  }
  .ts-title:before {
    left: -15px;
  }
  .ts-tranding-post .ts-title {
    margin-left: 0px;
  }
  .ts-tranding-post .carousel-inner .ts-post-thumb img {
    min-height: 260px;
  }
  /*- footer--*/
  .footer-social li {
    margin-right: 20px;
    margin-top: 15px;
  }
  .footer-logo {
    margin-bottom: 20px;
  }
  .newsletter-form .media {
    display: block;
  }
  .ts-newslatter .newsletter-form .ts-submit-btn {
    margin-left: 38px;
    margin-top: 20px;
  }
  .ts-footer .footer-menu ul li a {
    margin: 0 8px;
  }
  /*-- single post ---*/
  .single-post.ts-grid-box, .comments-form.ts-grid-box {
    padding: 30px 15px 30px;
  }
  .post-meta-info li {
    margin-right: 16px;
    margin-bottom: 10px;
  }
  .single-post .post-media {
    margin: 0;
  }
  blockquote {
    font-size: 17px;
    line-height: 35px;
    padding: 0 0 0 18px;
    margin: 50px 0;
  }
  blockquote cite {
    display: none;
  }
  p img.float-left {
    margin-right: 0;
    width: 100%;
  }
  .author-box {
    padding: 20px 0 20px 70px;
  }
  .author-box .author-img {
    width: 60px;
    height: 60px;
  }
  .author-box .authors-social {
    float: none;
    display: block;
    width: 100%;
    clear: both;
  }
  .author-box .authors-social a {
    margin-left: 16px;
  }
  .post-navigation .post-previous, .post-navigation .post-next {
    width: 100%;
  }
  .post-navigation .post-previous {
    margin-bottom: 20px;
    border-right: none;
  }
  /*---------- single post style -------------*/
  .post-layout-2 .single-big-img .entry-header {
    padding: 12px 27px;
  }
  .breadcrumb li {
    padding: 7px 26px 7px 17px;
  }
  /*------------------ category --------*/
  .ts-category-list {
    margin-bottom: 30px;
    margin-top: 10px;
  }
  .ts-category-list li a {
    padding: 2px 9px;
  }
  /*--- health  index css----*/
  .top-bar.top-bg .ts-date {
    text-align: center;
  }
  .top-bar.top-bg .top-nav {
    text-align: center;
  }
  .top-bar.top-bg .top-nav li a {
    margin-left: 20px;
  }
  .ts-post-style-2 .ts-overlay-style .ts-post-thumb img {
    min-height: 250px;
  }
  .ts-grid-item .item {
    margin-bottom: 20px;
  }
  .ts-grid-item .ts-grid-content.ts-list-post-box .item {
    margin-bottom: 0;
  }
  /*------- food index css -----*/
  .nav-icon-item .navigation {
    height: 70px;
  }
  .nav-icon-item .nav-menu li a i {
    text-align: left !important;
  }
  .nav-icon-item .ts-main-menu .right-menu li .nav-search .nav-search-button {
    text-align: left;
    padding: 25px 45px !important;
  }
  #featured-slider-3 {
    margin-bottom: 0;
  }
  .featured-content-box .post-content {
    padding: 15px;
    margin: -40px 15px 0px;
  }
  .item.post-content-box {
    margin-bottom: 15px;
  }
  .ts-pagination-1 {
    margin-bottom: 30px;
  }
  .ts-footer-2 .copyright-text {
    text-align: center;
    margin-bottom: 15px;
  }
  .footer-social-list.text-right {
    text-align: center !important;
  }
  /*-------------- technology index css --------*/
  .navigation-portrait .submenu-indicator {
    height: auto;
    top: 11px;
  }
  .header-transparent .navigation-portrait .nav-menus-wrapper {
    background: #000032;
  }
  .header-transparent .navigation-portrait .submenu-indicator {
    top: 27px;
  }
  .post-title.ex-lg {
    font-size: 30px;
  }
  #featured-slider-4 .ts-post-thumb img {
    min-height: inherit;
  }
  .ts-grid-features-box {
    padding: 45px 29px 23px;
  }
  .left-sidebar {
    margin-bottom: 30px;
  }
  .ts-footer.ts-footer-3 .footer-logo {
    padding: 34px 0 15px;
  }
  .ts-footer.ts-footer-3 .footer-menu li a:after {
    display: none;
  }
  .ts-footer.ts-footer-3 .footer-social-list li a {
    margin-left: 30px;
  }
  .ts-footer.ts-footer-3 .copyright-text {
    text-align: center !important;
  }
  /*----------------- business index css-------------*/
  .currency-list-item {
    margin-top: 20px;
  }
  .footer-widget {
    margin-bottom: 40px;
  }
  .ts-footer [class*="col-"]:last-of-type .footer-widget {
    margin-bottom: 0;
  }
  .ts-footer-4 .footer-logo {
    text-align: center;
  }
  .ts-footer-4 .footer-social-list {
    text-align: center !important;
  }
  .copyright-section .footer-menu,
  .copyright-section .copyright-text {
    text-align: center !important;
    margin: 10px 0;
  }
  /*--------------------- crypto index css ------------*/
  .header-box-right .navigation {
    height: 65px;
  }
  .header-box-right .header-logo {
    text-align: center;
    padding: 10px 0;
  }
  .header-box-right .ts-date {
    text-align: center;
  }
  .header-box-right .navigation:before {
    display: none;
  }
  .header-box-right .ts-top-nav {
    text-align: center !important;
  }
  .header-box-right .nav-menu > li.active {
    background: #232323;
    color: #fff;
  }
  .header-box-right .nav-menu > li > a {
    height: 60px;
  }
  .header-box-right .nav-menu > li > a:before {
    display: none;
  }
  .header-box-right .nav-toggle:before {
    background-color: #fff;
    border-radius: 10px;
    -webkit-box-shadow: 0 0.5em 0 0 #fff, 0 1em 0 0 #fff;
    box-shadow: 0 0.5em 0 0 #fff, 0 1em 0 0 #fff;
  }
  .breakink-news-section #breaking_slider .owl-nav {
    top: -32px;
  }
  .breakink-news-section #breaking_slider .owl-nav .owl-prev,
  .breakink-news-section #breaking_slider .owl-nav .owl-next {
    height: 26px;
  }
  .ts-footer-5 .footer-logo {
    text-align: left;
  }
  .ts-footer-5 .footer-social-list {
    text-align: left !important;
  }
  .ts-footer-5 .footer-widget.newsletter-widgets {
    padding-left: 0;
  }
  .category-post-style1 .item {
    margin-bottom: 20px;
  }
  /*------------- home default three ------*/
  .header-middle .header-logo {
    margin-bottom: 15px;
  }
  .header-standerd .nav-menu > li > a {
    color: #232323;
  }
  .header-standerd .nav-toggle:before {
    background-color: #ffffff;
    -webkit-box-shadow: 0 0.5em 0 0 #ffffff, 0 1em 0 0 #ffffff;
    box-shadow: 0 0.5em 0 0 #ffffff, 0 1em 0 0 #ffffff;
  }
  .header-standerd .nav-menu > li.active > a {
    color: #fff;
  }
  .xs-left {
    text-align: left !important;
  }
  /*----------- index default four -------------*/
  .navbar-standerd .navigation-portrait .nav-brand {
    line-height: 70px;
  }
  .navbar-standerd .nav-menu > li > a {
    height: 60px;
  }
  .navbar-standerd.nav-item .nav-menu > li > a {
    line-height: 35px;
  }
  .ts-post-style-2 .ts-overlay-style .item {
    min-height: 360px;
  }
  .header-middle.v4 {
    padding: 13px 0;
  }
  .megamenu-panel .item {
    margin-bottom: 15px;
  }
  .error-page {
    padding: 50px 15px;
  }
  .error-page .error-code h2 {
    font-size: 130px;
    line-height: 130px;
  }
  .error-page .error-message h3 {
    font-size: 25px;
  }
  .error-page .error-body h4 {
    font-size: 14px;
  }
  .nav-menu .megamenu-panel {
    -webkit-box-shadow: none;
    box-shadow: none;
    padding: 0 15px;
  }
  .ad-widget,
  .posts-ad {
    text-align: center;
  }
  .ts-grid-box.most-populer-item,
  .ts-post-overlay-style-1 .ts-overlay-style:last-of-type {
    margin-bottom: 0;
  }
  #more-news-section {
    padding-top: 0;
  }
  .ts-breaking-news .breaking-news-content {
    width: 100%;
  }
  .category-box-item-3 .item,
  .category-item .item {
    margin-bottom: 20px;
  }
  .video-slider .post-video img {
    min-height: 360px;
  }
  .video-slider .post-video .post-video-content {
    padding: 15px;
  }
  .ts-grid-box.ts-category-title {
    padding: 18px 15px;
  }
  .category-list-item .widget-title:before {
    left: -15px;
  }
  .mr--20 {
    margin-right: 0;
  }
  .ts-pagination.text-center.mt-15 {
    margin-bottom: 30px;
  }
  .nav-menu-item.nav-icon-item .navigation {
    height: 70px;
  }
  /*-------------------- sport index -----------*/
  .top-bar.transparent #breaking_slider .owl-nav {
    top: -28px;
    right: 0;
  }
  .header-box-transprent {
    top: 100px;
  }
  .header-box-transprent .ts-main-menu .nav-menu > li > a {
    color: #232323;
  }
  .hero-content h2 {
    font-size: 30px;
  }
  .slider-dot-item {
    left: 0;
    padding-left: 84px;
  }
  .tab-menu-item {
    margin-bottom: 20px;
  }
  .tab-menu-item li a {
    padding: 0 8px;
  }
  .video-item {
    height: 300px;
  }
  .footer-top .footer-menu ul li a {
    margin: 0 13px;
  }
  .footer-top .footer-logo {
    margin-top: 20px;
    margin-bottom: 30px;
  }
  .footer-bottom .footer-social-list {
    margin: 20px 0;
  }
  .footer-bottom .footer-social-list li a {
    margin: 0 17px;
  }
  .p-10 {
    padding: 0 15px;
  }
  /*----------------- travel index css ------------*/
  .header-transprent {
    top: 25px;
  }
  .header-transprent .nav-menu > li > a:hover {
    color: #232232;
  }
  .header-transprent .nav-menu > li > a .submenu-indicator {
    margin-top: 14px;
  }
  .header-transprent .nav-menu > li:first-child > a {
    padding-left: 15px;
  }
  .header-transprent .nav-toggle:before {
    background-color: #fff;
    border-radius: 10px;
    -webkit-box-shadow: 0 0.5em 0 0 #fff, 0 1em 0 0 #fff;
    box-shadow: 0 0.5em 0 0 #fff, 0 1em 0 0 #fff;
  }
  .header-transprent .mobile_hidden {
    display: none;
  }
  .header-transprent .nav-menu-centered .header-search {
    display: none;
  }
  .header-box-transprent .ts-main-menu .nav-menu > li:first-child a {
    padding-left: 20px;
  }
  .featured-area .item {
    height: 600px;
  }
  .featured-area .item .hero-content h2 {
    font-size: 30px;
  }
  .featured-area .item .hero-content .featurd-video-icon {
    font-size: 58px;
  }
  #post-slider1 .owl-nav {
    left: 0;
  }
  .post-overflow-style .post-content-item {
    padding: 70px 20px 20px 40px;
    margin-left: 0;
  }
  .block-wrapper-1.p-50 {
    padding: 30px 0;
    margin: 0;
  }
  .grid-style-2 .ts-overlay-style .item {
    min-height: 350px;
  }
  .accordion-post-style {
    margin-bottom: 30px;
  }
  .col-lg-3.pl-0 .accordion-post-style {
    margin-bottom: 0px;
  }
  .watching-section .item {
    margin-bottom: 20px;
  }
  .footer .copyright-text {
    margin-bottom: 20px;
    text-align: center;
  }
  section.block-wrapper.block-wrapper-1.p-50.mb-40,
  section.block-wrapper.block-wrapper-1.p-50.mt-50 {
    padding-bottom: 0;
  }
  section.block-wrapper.block-wrapper-1.p-100 {
    padding: 30px 0;
  }
  .navbar-standerd .top-social {
    padding-left: 10px;
  }
  .navbar-standerd .top-social li {
    margin-right: 0;
  }
  .block-wrapper.p-60 {
    padding: 40px 0;
  }
  .blog-post-slider-item .ts-grid-box {
    padding: 34px 20px 50px 30px;
    margin-bottom: 0;
  }
  .blog-post-slider-item .ts-post-thumb {
    margin: 0;
  }
  .blog-post-slider-item.mb-60 {
    margin-bottom: 30px;
  }
  .post-col-list-item .older-post-btn {
    margin-bottom: 40px;
  }
  .post-col-list-item [class*="col-"]:last-of-type {
    margin-bottom: 0;
  }
  .instagramPhoto .col-md.p-0 {
    margin-right: 0;
  }
  .category-widget .widget-title:before {
    left: -15px;
  }
  .ts-grid-box.widgets.tag-list .widget-title:before {
    left: -15px;
  }
  .logo-area {
    padding: 30px 15px;
  }
  .post-layout-7 .single-big-img .entry-header-item {
    padding-top: 245px;
  }
  .social-widget .widget-title:before {
    left: -15px;
  }
  .entry-cat-header .ts-title {
    margin-bottom: 20px;
  }
  .category-layout-3 .col-lg-9 .row .col-lg-6.mb-30 {
    margin-bottom: 0 !important;
  }
  .post-col-list-item .col-lg-6.mb-30 {
    margin-bottom: 0 !important;
  }
  .ts-post-style-3 .col-lg-4.p-1:last-of-type .ts-overlay-style {
    margin-bottom: 0;
  }
  .ts-col-list-post .col-lg-4.col-md-6.mb-30 {
    margin-bottom: 0 !important;
  }
  .navbar-standerd.nav-bar-dark .navigation-portrait .nav-menus-wrapper {
    background: #232323;
  }
  .navbar-standerd.nav-bar-dark .navigation-portrait .nav-menu > li {
    border-top: solid 1px #444444;
  }
  .widget-banner {
    text-align: center;
  }
  .uk-width-3-4 {
    width: 100%;
  }
  .col-lg-3.pr-10 .ts-post-overlay-style-1 {
    margin-bottom: 30px;
  }
  .post-list-box.ts-list-post-box.ts-grid-content {
    margin-bottom: 20px;
  }
  .post-list-box.ts-list-post-box.ts-grid-content .ts-overlay-style {
    margin-bottom: 10px;
  }
  #featured-slider-5 {
    margin-bottom: 0;
  }
  .block-wrapper.ts-post-style-4 {
    padding-bottom: 0;
  }
  #most-pupuler {
    margin-bottom: 30px;
  }
  .ts-overlay-item.ts-grid-style-2 [class*='col-']:last-of-type .ts-overlay-style {
    margin-bottom: 0;
  }
  .ts-grid-item.ts-overlay-item [class*='col-']:last-of-type .ts-overlay-style {
    margin-bottom: 0;
  }
  .featured-tab-item .featured-tab-post > li {
    border-right: none;
    border-bottom: 1px solid #ddd;
  }
  .category-layout-1 [class*='col-']:last-of-type .ts-overlay-style {
    margin-bottom: 0;
  }
  .category-layout-5 .ts-grid-box.entry-header {
    margin-bottom: 0;
  }
  .featured-post.post-style-4 .ts-overlay-style {
    margin-bottom: 30px;
  }
  .featured-post.post-style-4 [class*='col-']:last-of-type .ts-overlay-style {
    margin-bottom: 0px;
  }
  .ts-content-box-item .row.mb-30 {
    margin-bottom: 0 !important;
  }
  .ts-pagination.text-center.mb-20,
  .xs-mb-30,
  .ts-grid-style-3 .ts-overlay-style {
    margin-bottom: 30px;
  }
  .plr-10 {
    padding: 0 15px;
  }
  .ts-grid-style-3 .ts-overlay-style:last-of-type {
    margin-bottom: 0;
  }
  .ts-tranding-post .slider-indicators.carousel-indicators > li:before,
  .ts-tranding-post .slider-indicators.carousel-indicators > li:after {
    display: none;
  }
  .widget-banner {
    text-align: center;
  }
  .ts-footer-5 .footer-widget.border-right:after {
    display: none;
  }
  #featured-slider-4 .item {
    min-height: 365px;
  }
}

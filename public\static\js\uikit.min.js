/*! UIkit 3.0.0-rc.10 | http://www.getuikit.com | (c) 2014 - 2018 YOOtheme | MIT License */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define("uikit",e):t.UIkit=e()}(this,function(){"use strict";function p(i,n){return function(t){var e=arguments.length;return e?1<e?i.apply(n,arguments):i.call(n,t):i.call(n)}}var e=Object.prototype,i=e.hasOwnProperty;function h(t,e){return i.call(t,e)}var n={},r=/([a-z\d])([A-Z])/g;function m(t){return t in n||(n[t]=t.replace(r,"$1-$2").toLowerCase()),n[t]}var o=/-(\w)/g;function g(t){return t.replace(o,s)}function s(t,e){return e?e.toUpperCase():""}function a(t){return t.length?s(0,t.charAt(0))+t.slice(1):""}var t=String.prototype,l=t.startsWith||function(t){return 0===this.lastIndexOf(t,0)};function v(t,e){return l.call(t,e)}var c=t.endsWith||function(t){return this.substr(-t.length)===t};function u(t,e){return c.call(t,e)}var d=function(t){return~this.indexOf(t)},f=t.includes||d,w=Array.prototype.includes||d;function b(t,e){return t&&(N(t)?f:w).call(t,e)}var y=Array.isArray;function x(t){return"function"==typeof t}function k(t){return null!==t&&"object"==typeof t}function $(t){return k(t)&&Object.getPrototypeOf(t)===e}function I(t){return k(t)&&t===t.window}function S(t){return k(t)&&9===t.nodeType}function T(t){return k(t)&&!!t.jquery}function E(t){return t instanceof Node||k(t)&&1===t.nodeType}var C=e.toString;function _(t){return C.call(t).match(/^\[object (NodeList|HTMLCollection)\]$/)}function A(t){return"boolean"==typeof t}function N(t){return"string"==typeof t}function O(t){return"number"==typeof t}function M(t){return O(t)||N(t)&&!isNaN(t-parseFloat(t))}function D(t){return void 0===t}function B(t){return A(t)?t:"true"===t||"1"===t||""===t||"false"!==t&&"0"!==t&&t}function z(t){var e=Number(t);return!isNaN(e)&&e}function P(t){return parseFloat(t)||0}function H(t){return E(t)||I(t)||S(t)?t:_(t)||T(t)?t[0]:y(t)?H(t[0]):null}var W=Array.prototype;function L(t){return E(t)?[t]:_(t)?W.slice.call(t):y(t)?t.map(H).filter(Boolean):T(t)?t.toArray():[]}function j(t){return y(t)?t:N(t)?t.split(/,(?![^(]*\))/).map(function(t){return M(t)?z(t):B(t.trim())}):[t]}function F(t){return t?u(t,"ms")?P(t):1e3*P(t):0}function V(t,e,i){return t.replace(new RegExp(e+"|"+i,"mg"),function(t){return t===e?i:e})}var Y=Object.assign||function(t){for(var e=[],i=arguments.length-1;0<i--;)e[i]=arguments[i+1];t=Object(t);for(var n=0;n<e.length;n++){var r=e[n];if(null!==r)for(var o in r)h(r,o)&&(t[o]=r[o])}return t};function R(t,e){for(var i in t)e.call(t[i],t[i],i)}function q(t,i){return t.sort(function(t,e){return t[i]>e[i]?1:e[i]>t[i]?-1:0})}function U(t,e,i){return void 0===e&&(e=0),void 0===i&&(i=1),Math.min(Math.max(t,e),i)}function X(){}function J(t,e){return t.left<e.right&&t.right>e.left&&t.top<e.bottom&&t.bottom>e.top}function K(t,e){return t.x<=r2.right&&t.x>=r2.left&&t.y<=r2.bottom&&t.y>=r2.top}var G={ratio:function(t,e,i){var n,r="width"===e?"height":"width";return(n={})[r]=t[e]?Math.round(i*t[r]/t[e]):t[r],n[e]=i,n},contain:function(i,n){var r=this;return R(i=Y({},i),function(t,e){return i=i[e]>n[e]?r.ratio(i,e,n[e]):i}),i},cover:function(i,n){var r=this;return R(i=this.contain(i,n),function(t,e){return i=i[e]<n[e]?r.ratio(i,e,n[e]):i}),i}};function Z(t,e,i){if(k(e))for(var n in e)Z(t,n,e[n]);else{if(D(i))return(t=H(t))&&t.getAttribute(e);L(t).forEach(function(t){x(i)&&(i=i.call(t,Z(t,e))),null===i?tt(t,e):t.setAttribute(e,i)})}}function Q(t,e){return L(t).some(function(t){return t.hasAttribute(e)})}function tt(t,e){t=L(t),e.split(" ").forEach(function(e){return t.forEach(function(t){return t.removeAttribute(e)})})}function et(t,e,i,n){Z(t,e,function(t){return t?t.replace(i,n):t})}function it(t,e){for(var i=0,n=[e,"data-"+e];i<n.length;i++)if(Q(t,n[i]))return Z(t,n[i])}function nt(t,e){return H(t)||ot(t,ct(t)?e:document)}function rt(t,e){var i=L(t);return i.length&&i||st(t,ct(t)?e:document)}function ot(t,e){return H(at(t,e,"querySelector"))}function st(t,e){return L(at(t,e,"querySelectorAll"))}function at(t,s,e){if(void 0===s&&(s=document),!t||!N(t))return null;var a;ct(t=t.replace(ht,"$1 *"))&&(a=[],t=t.split(",").map(function(t,e){var i=s;if("!"===(t=t.trim())[0]){var n=t.substr(1).trim().split(" ");i=mt(s.parentNode,n[0]),t=n.slice(1).join(" ").trim()}if("-"===t[0]){var r=t.substr(1).trim().split(" "),o=(i||s).previousElementSibling;i=ft(o,t.substr(1))?o:null,t=r.slice(1).join(" ")}return i?(i.id||(i.id="uk-"+Date.now()+e,a.push(function(){return tt(i,"id")})),"#"+wt(i.id)+" "+t):null}).filter(Boolean).join(","),s=document);try{return s[e](t)}catch(t){return null}finally{a&&a.forEach(function(t){return t()})}}var lt=/(^|,)\s*[!>+~-]/,ht=/([!>+~-])(?=\s+[!>+~-]|\s*$)/g;function ct(t){return N(t)&&t.match(lt)}var ut=Element.prototype,dt=ut.matches||ut.webkitMatchesSelector||ut.msMatchesSelector;function ft(t,e){return L(t).some(function(t){return dt.call(t,e)})}var pt=ut.closest||function(t){var e=this;do{if(ft(e,t))return e;e=e.parentNode}while(e&&1===e.nodeType)};function mt(t,e){return v(e,">")&&(e=e.slice(1)),E(t)?t.parentNode&&pt.call(t,e):L(t).map(function(t){return t.parentNode&&pt.call(t,e)}).filter(Boolean)}function gt(t,e){for(var i=[],n=H(t).parentNode;n&&1===n.nodeType;)ft(n,e)&&i.push(n),n=n.parentNode;return i}var vt=window.CSS&&CSS.escape||function(t){return t.replace(/([^\x7f-\uFFFF\w-])/g,function(t){return"\\"+t})};function wt(t){return N(t)?vt.call(null,t):""}var bt={area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,menuitem:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0};function yt(t){return L(t).some(function(t){return bt[t.tagName.toLowerCase()]})}function xt(t){return L(t).some(function(t){return t.offsetWidth||t.offsetHeight||t.getClientRects().length})}var kt="input,select,textarea,button";function $t(t){return L(t).some(function(t){return ft(t,kt)})}function It(t,e){return L(t).filter(function(t){return ft(t,e)})}function St(t,e){return N(e)?ft(t,e)||mt(t,e):t===e||(S(e)?e.documentElement:H(e)).contains(H(t))}function Tt(){for(var t=[],e=arguments.length;e--;)t[e]=arguments[e];var i,n=Nt(t),r=n[0],o=n[1],s=n[2],a=n[3],l=n[4];return r=Dt(r),s&&(a=function(t,n,r){var o=this;return function(i){t.forEach(function(t){var e=">"===n[0]?st(n,t).reverse().filter(function(t){return St(i.target,t)})[0]:mt(i.target,n);e&&(i.delegate=t,i.current=e,r.call(o,i))})}}(r,s,a)),1<a.length&&(i=a,a=function(t){return y(t.detail)?i.apply(void 0,[t].concat(t.detail)):i(t)}),o.split(" ").forEach(function(e){return r.forEach(function(t){return t.addEventListener(e,a,l)})}),function(){return Et(r,o,a,l)}}function Et(t,e,i,n){void 0===n&&(n=!1),t=Dt(t),e.split(" ").forEach(function(e){return t.forEach(function(t){return t.removeEventListener(e,i,n)})})}function Ct(){for(var t=[],e=arguments.length;e--;)t[e]=arguments[e];var i=Nt(t),n=i[0],r=i[1],o=i[2],s=i[3],a=i[4],l=i[5],h=Tt(n,r,o,function(t){var e=!l||l(t);e&&(h(),s(t,e))},a);return h}function _t(t,i,n){return Dt(t).reduce(function(t,e){return t&&e.dispatchEvent(At(i,!0,!0,n))},!0)}function At(t,e,i,n){if(void 0===e&&(e=!0),void 0===i&&(i=!1),N(t)){var r=document.createEvent("CustomEvent");r.initCustomEvent(t,e,i,n),t=r}return t}function Nt(t){return x(t[2])&&t.splice(2,0,!1),t}function Ot(t){return"EventTarget"in window?t instanceof EventTarget:t&&"addEventListener"in t}function Mt(t){return Ot(t)?t:H(t)}function Dt(t){return y(t)?t.map(Mt).filter(Boolean):N(t)?st(t):Ot(t)?[t]:L(t)}function Bt(){var e=setTimeout(Ct(document,"click",function(t){t.preventDefault(),t.stopImmediatePropagation(),clearTimeout(e)},!0));_t(document,"touchcancel")}var zt="Promise"in window?window.Promise:Lt,Pt=function(){var i=this;this.promise=new zt(function(t,e){i.reject=e,i.resolve=t})},Ht=2,Wt="setImmediate"in window?setImmediate:setTimeout;function Lt(t){this.state=Ht,this.value=void 0,this.deferred=[];var e=this;try{t(function(t){e.resolve(t)},function(t){e.reject(t)})}catch(t){e.reject(t)}}Lt.reject=function(i){return new Lt(function(t,e){e(i)})},Lt.resolve=function(i){return new Lt(function(t,e){t(i)})},Lt.all=function(s){return new Lt(function(i,t){var n=[],r=0;function e(e){return function(t){n[e]=t,(r+=1)===s.length&&i(n)}}0===s.length&&i(n);for(var o=0;o<s.length;o+=1)Lt.resolve(s[o]).then(e(o),t)})},Lt.race=function(n){return new Lt(function(t,e){for(var i=0;i<n.length;i+=1)Lt.resolve(n[i]).then(t,e)})};var jt=Lt.prototype;function Ft(s,a){return new zt(function(t,e){var i=Y({data:null,method:"GET",headers:{},xhr:new XMLHttpRequest,beforeSend:X,responseType:""},a);i.beforeSend(i);var n=i.xhr;for(var r in i)if(r in n)try{n[r]=i[r]}catch(t){}for(var o in n.open(i.method.toUpperCase(),s),i.headers)n.setRequestHeader(o,i.headers[o]);Tt(n,"load",function(){0===n.status||200<=n.status&&n.status<300||304===n.status?t(n):e(Y(Error(n.statusText),{xhr:n,status:n.status}))}),Tt(n,"error",function(){return e(Y(Error("Network Error"),{xhr:n}))}),Tt(n,"timeout",function(){return e(Y(Error("Network Timeout"),{xhr:n}))}),n.send(i.data)})}function Vt(n,r,o){return new zt(function(t,e){var i=new Image;i.onerror=e,i.onload=function(){return t(i)},i.src=n,r&&(i.srcset=r),o&&(i.sizes=o)})}function Yt(){return"complete"===document.readyState||"loading"!==document.readyState&&!document.documentElement.doScroll}function Rt(t){if(Yt())t();else var e=function(){i(),n(),t()},i=Tt(document,"DOMContentLoaded",e),n=Tt(window,"load",e)}function qt(t,e){return e?L(t).indexOf(H(e)):L((t=H(t))&&t.parentNode.children).indexOf(t)}function Ut(t,e,i,n){void 0===i&&(i=0),void 0===n&&(n=!1);var r=(e=L(e)).length;return t=M(t)?z(t):"next"===t?i+1:"previous"===t?i-1:qt(e,t),n?U(t,0,r-1):(t%=r)<0?t+r:t}function Xt(t){return(t=H(t)).innerHTML="",t}function Jt(t,e){return t=H(t),D(e)?t.innerHTML:Kt(t.hasChildNodes()?Xt(t):t,e)}function Kt(e,t){return e=H(e),Qt(t,function(t){return e.appendChild(t)})}function Gt(e,t){return e=H(e),Qt(t,function(t){return e.parentNode.insertBefore(t,e)})}function Zt(e,t){return e=H(e),Qt(t,function(t){return e.nextSibling?Gt(e.nextSibling,t):Kt(e.parentNode,t)})}function Qt(t,e){return(t=N(t)?se(t):t)?"length"in t?L(t).map(e):e(t):null}function te(t){L(t).map(function(t){return t.parentNode&&t.parentNode.removeChild(t)})}function ee(t,e){for(e=H(Gt(t,e));e.firstChild;)e=e.firstChild;return Kt(e,t),e}function ie(t,e){return L(L(t).map(function(t){return t.hasChildNodes?ee(L(t.childNodes),e):Kt(t,e)}))}function ne(t){L(t).map(function(t){return t.parentNode}).filter(function(t,e,i){return i.indexOf(t)===e}).forEach(function(t){Gt(t,t.childNodes),te(t)})}jt.resolve=function(t){var e=this;if(e.state===Ht){if(t===e)throw new TypeError("Promise settled with itself.");var i=!1;try{var n=t&&t.then;if(null!==t&&k(t)&&x(n))return void n.call(t,function(t){i||e.resolve(t),i=!0},function(t){i||e.reject(t),i=!0})}catch(t){return void(i||e.reject(t))}e.state=0,e.value=t,e.notify()}},jt.reject=function(t){var e=this;if(e.state===Ht){if(t===e)throw new TypeError("Promise settled with itself.");e.state=1,e.value=t,e.notify()}},jt.notify=function(){var o=this;Wt(function(){if(o.state!==Ht)for(;o.deferred.length;){var t=o.deferred.shift(),e=t[0],i=t[1],n=t[2],r=t[3];try{0===o.state?x(e)?n(e.call(void 0,o.value)):n(o.value):1===o.state&&(x(i)?n(i.call(void 0,o.value)):r(o.value))}catch(t){r(t)}}})},jt.then=function(i,n){var r=this;return new Lt(function(t,e){r.deferred.push([i,n,t,e]),r.notify()})},jt.catch=function(t){return this.then(void 0,t)};var re=/^\s*<(\w+|!)[^>]*>/,oe=/^<(\w+)\s*\/?>(?:<\/\1>)?$/;function se(t){var e=oe.exec(t);if(e)return document.createElement(e[1]);var i=document.createElement("div");return re.test(t)?i.insertAdjacentHTML("beforeend",t.trim()):i.textContent=t,1<i.childNodes.length?L(i.childNodes):i.firstChild}function ae(t,e){if(t&&1===t.nodeType)for(e(t),t=t.firstElementChild;t;)ae(t,e),t=t.nextElementSibling}function le(t){for(var e=[],i=arguments.length-1;0<i--;)e[i]=arguments[i+1];pe(t,e,"add")}function he(t){for(var e=[],i=arguments.length-1;0<i--;)e[i]=arguments[i+1];pe(t,e,"remove")}function ce(t,e){et(t,"class",new RegExp("(^|\\s)"+e+"(?!\\S)","g"),"")}function ue(t){for(var e=[],i=arguments.length-1;0<i--;)e[i]=arguments[i+1];e[0]&&he(t,e[0]),e[1]&&le(t,e[1])}function de(t,e){return L(t).some(function(t){return t.classList.contains(e)})}function fe(t){for(var n=[],e=arguments.length-1;0<e--;)n[e]=arguments[e+1];if(n.length){var r=N((n=me(n))[n.length-1])?[]:n.pop();n=n.filter(Boolean),L(t).forEach(function(t){for(var e=t.classList,i=0;i<n.length;i++)ve.Force?e.toggle.apply(e,[n[i]].concat(r)):e[(D(r)?!e.contains(n[i]):r)?"add":"remove"](n[i])})}}function pe(t,i,n){(i=me(i).filter(Boolean)).length&&L(t).forEach(function(t){var e=t.classList;ve.Multiple?e[n].apply(e,i):i.forEach(function(t){return e[n](t)})})}function me(t){return t.reduce(function(t,e){return t.concat.call(t,N(e)&&b(e," ")?e.trim().split(" "):e)},[])}var ge,ve={};(ge=document.createElement("_").classList)&&(ge.add("a","b"),ge.toggle("c",!1),ve.Multiple=ge.contains("b"),ve.Force=!ge.contains("c"));var we={"animation-iteration-count":!(ge=null),"column-count":!0,"fill-opacity":!0,"flex-grow":!0,"flex-shrink":!0,"font-weight":!0,"line-height":!0,opacity:!0,order:!0,orphans:!0,widows:!0,"z-index":!0,zoom:!0};function be(t,e,r){return L(t).map(function(i){if(N(e)){if(e=Se(e),D(r))return xe(i,e);r||0===r?i.style[e]=M(r)&&!we[e]?r+"px":r:i.style.removeProperty(e)}else{if(y(e)){var n=ye(i);return e.reduce(function(t,e){return t[e]=n[Se(e)],t},{})}k(e)&&R(e,function(t,e){return be(i,e,t)})}return i})[0]}function ye(t,e){return(t=H(t)).ownerDocument.defaultView.getComputedStyle(t,e)}function xe(t,e,i){return ye(t,i)[e]}var ke={};function $e(t){if(!(t in ke)){var e=Kt(document.documentElement,document.createElement("div"));le(e,"var-"+t);try{ke[t]=xe(e,"content",":before").replace(/^["'](.*)["']$/,"$1"),ke[t]=JSON.parse(ke[t])}catch(t){}document.documentElement.removeChild(e)}return ke[t]}var Ie={};function Se(t){var e=Ie[t];return e||(e=Ie[t]=function(t){if((t=m(t))in Ee)return t;var e,i=Te.length;for(;i--;)if((e="-"+Te[i]+"-"+t)in Ee)return e}(t)||t),e}var Te=["webkit","moz","ms"],Ee=document.createElement("_").style;function Ce(t,s,a,l){return void 0===a&&(a=400),void 0===l&&(l="linear"),zt.all(L(t).map(function(o){return new zt(function(i,n){for(var t in s){var e=be(o,t);""===e&&be(o,t,e)}var r=setTimeout(function(){return _t(o,"transitionend")},a);Ct(o,"transitionend transitioncanceled",function(t){var e=t.type;clearTimeout(r),he(o,"uk-transition"),be(o,{"transition-property":"","transition-duration":"","transition-timing-function":""}),"transitioncanceled"===e?n():i()},!1,function(t){var e=t.target;return o===e}),le(o,"uk-transition"),be(o,Y({"transition-property":Object.keys(s).map(Se).join(","),"transition-duration":a+"ms","transition-timing-function":l},s))})}))}var _e={start:Ce,stop:function(t){return _t(t,"transitionend"),zt.resolve()},cancel:function(t){_t(t,"transitioncanceled")},inProgress:function(t){return de(t,"uk-transition")}},Ae="uk-animation-",Ne="uk-cancel-animation";function Oe(t,e,i,a,l){var h=arguments;return void 0===i&&(i=200),zt.all(L(t).map(function(s){return new zt(function(n,r){if(de(s,Ne))requestAnimationFrame(function(){return zt.resolve().then(function(){return Oe.apply(void 0,h).then(n,r)})});else{var t=e+" "+Ae+(l?"leave":"enter");v(e,Ae)&&(a&&(t+=" uk-transform-origin-"+a),l&&(t+=" "+Ae+"reverse")),o(),Ct(s,"animationend animationcancel",function(t){var e=t.type,i=!1;"animationcancel"===e?(r(),o()):(n(),zt.resolve().then(function(){i=!0,o()})),requestAnimationFrame(function(){i||(le(s,Ne),requestAnimationFrame(function(){return he(s,Ne)}))})},!1,function(t){var e=t.target;return s===e}),be(s,"animationDuration",i+"ms"),le(s,t)}function o(){be(s,"animationDuration",""),ce(s,Ae+"\\S*")}})}))}var Me=new RegExp(Ae+"(enter|leave)"),De={in:function(t,e,i,n){return Oe(t,e,i,n,!1)},out:function(t,e,i,n){return Oe(t,e,i,n,!0)},inProgress:function(t){return Me.test(Z(t,"class"))},cancel:function(t){_t(t,"animationcancel")}};function Be(t,e){return N(t)?Pe(t)?H(se(t)):ot(t,e):H(t)}function ze(t,e){return N(t)?Pe(t)?L(se(t)):st(t,e):L(t)}function Pe(t){return"<"===t[0]||t.match(/^\s*</)}var He={width:["x","left","right"],height:["y","top","bottom"]};function We(t,e,c,u,d,i,f,p){c=Xe(c),u=Xe(u);var m={element:c,target:u};if(!t||!e)return m;var g=je(t),v=je(e),w=v;return Ue(w,c,g,-1),Ue(w,u,v,1),d=Je(d,g.width,g.height),i=Je(i,v.width,v.height),d.x+=i.x,d.y+=i.y,w.left+=d.x,w.top+=d.y,p=je(p||ei(t)),f&&R(He,function(t,n){var r=t[0],o=t[1],s=t[2];if(!0===f||b(f,r)){var e=c[r]===o?-g[n]:c[r]===s?g[n]:0,i=u[r]===o?v[n]:u[r]===s?-v[n]:0;if(w[o]<p[o]||w[o]+g[n]>p[s]){var a=g[n]/2,l="center"===u[r]?-v[n]/2:0;"center"===c[r]&&(h(a,l)||h(-a,-l))||h(e,i)}}function h(e,t){var i=w[o]+e+t-2*d[r];if(i>=p[o]&&i+g[n]<=p[s])return w[o]=i,["element","target"].forEach(function(t){m[t][r]=e?m[t][r]===He[n][1]?He[n][2]:He[n][1]:m[t][r]}),!0}}),Le(t,w),m}function Le(i,n){if(i=H(i),!n)return je(i);var r=Le(i),o=be(i,"position");["left","top"].forEach(function(t){if(t in n){var e=be(i,t);be(i,t,n[t]-r[t]+P("absolute"===o&&"auto"===e?Fe(i)[t]:e))}})}function je(t){var e,i,n=ei(t=H(t)),r=n.pageYOffset,o=n.pageXOffset;if(I(t)){var s=t.innerHeight,a=t.innerWidth;return{top:r,left:o,height:s,width:a,bottom:r+s,right:o+a}}xt(t)||(e=Z(t,"style"),i=Z(t,"hidden"),Z(t,{style:(e||"")+";display:block !important;",hidden:null}));var l=t.getBoundingClientRect();return D(e)||Z(t,{style:e,hidden:i}),{height:l.height,width:l.width,top:l.top+r,left:l.left+o,bottom:l.bottom+r,right:l.right+o}}function Fe(n){var r=(n=H(n)).offsetParent||ii(n).documentElement,o=Le(r),t=["top","left"].reduce(function(t,e){var i=a(e);return t[e]-=o[e]+P(be(n,"margin"+i))+P(be(r,"border"+i+"Width")),t},Le(n));return{top:t.top,left:t.left}}var Ve=Re("height"),Ye=Re("width");function Re(n){var r=a(n);return function(t,e){if(t=H(t),D(e)){if(I(t))return t["inner"+r];if(S(t)){var i=t.documentElement;return Math.max(i["offset"+r],i["scroll"+r])}return(e="auto"===(e=be(t,n))?t["offset"+r]:P(e)||0)-qe(n,t)}be(t,n,e||0===e?+e+qe(n,t)+"px":"")}}function qe(t,i){return"border-box"===be(i,"boxSizing")?He[t].slice(1).map(a).reduce(function(t,e){return t+P(be(i,"padding"+e))+P(be(i,"border"+e+"Width"))},0):0}function Ue(o,s,a,l){R(He,function(t,e){var i=t[0],n=t[1],r=t[2];s[i]===r?o[n]+=a[e]*l:"center"===s[i]&&(o[n]+=a[e]*l/2)})}function Xe(t){var e=/left|center|right/,i=/top|center|bottom/;return 1===(t=(t||"").split(" ")).length&&(t=e.test(t[0])?t.concat(["center"]):i.test(t[0])?["center"].concat(t):["center","center"]),{x:e.test(t[0])?t[0]:"center",y:i.test(t[1])?t[1]:"center"}}function Je(t,e,i){var n=(t||"").split(" "),r=n[0],o=n[1];return{x:r?P(r)*(u(r,"%")?e/100:1):0,y:o?P(o)*(u(o,"%")?i/100:1):0}}function Ke(t){switch(t){case"left":return"right";case"right":return"left";case"top":return"bottom";case"bottom":return"top";default:return t}}function Ge(t,e,i,n){if(void 0===e&&(e=0),void 0===i&&(i=0),!xt(t))return!1;var r=ei(t=H(t));if(n)return J(t.getBoundingClientRect(),{top:-e,left:-i,bottom:e+Ve(r),right:i+Ye(r)});var o=ti(t),s=o[0],a=o[1],l=r.pageYOffset,h=r.pageXOffset;return J({top:s,left:a,bottom:s+t.offsetHeight,right:s+t.offsetWidth},{top:l-e,left:h-i,bottom:l+e+Ve(r),right:h+i+Ye(r)})}function Ze(t,e){if(void 0===e&&(e=0),!xt(t))return 0;var i=ei(t=H(t)),n=ii(t),r=t.offsetHeight+e,o=ti(t)[0],s=Ve(i),a=s+Math.min(0,o-s),l=Math.max(0,s-(Ve(n)+e-(o+r)));return U((a+i.pageYOffset-o)/((a+(r-(l<s?l:0)))/100)/100)}function Qe(t,e){if(I(t=H(t))||S(t)){var i=ei(t);(0,i.scrollTo)(i.pageXOffset,e)}else t.scrollTop=e}function ti(t){var e=[0,0];do{if(e[0]+=t.offsetTop,e[1]+=t.offsetLeft,"fixed"===be(t,"position")){var i=ei(t);return e[0]+=i.pageYOffset,e[1]+=i.pageXOffset,e}}while(t=t.offsetParent);return e}function ei(t){return I(t)?t:ii(t).defaultView}function ii(t){return H(t).ownerDocument}var ni=/msie|trident/i.test(window.navigator.userAgent),ri="rtl"===Z(document.documentElement,"dir"),oi="ontouchstart"in window,si=window.PointerEvent,ai=oi||window.DocumentTouch&&document instanceof DocumentTouch||navigator.maxTouchPoints,li=ai?"mousedown "+(oi?"touchstart":"pointerdown"):"mousedown",hi=ai?"mousemove "+(oi?"touchmove":"pointermove"):"mousemove",ci=ai?"mouseup "+(oi?"touchend":"pointerup"):"mouseup",ui=ai&&si?"pointerenter":"mouseenter",di=ai&&si?"pointerleave":"mouseleave",fi={reads:[],writes:[],read:function(t){return this.reads.push(t),pi(),t},write:function(t){return this.writes.push(t),pi(),t},clear:function(t){return gi(this.reads,t)||gi(this.writes,t)},flush:function(){mi(this.reads),mi(this.writes.splice(0,this.writes.length)),this.scheduled=!1,(this.reads.length||this.writes.length)&&pi()}};function pi(){fi.scheduled||(fi.scheduled=!0,requestAnimationFrame(fi.flush.bind(fi)))}function mi(t){for(var e;e=t.shift();)e()}function gi(t,e){var i=t.indexOf(e);return!!~i&&!!t.splice(i,1)}function vi(){}function wi(t,e){return(e.y-t.y)/(e.x-t.x)}vi.prototype={positions:[],position:null,init:function(){var n=this;this.positions=[],this.position=null;var r=!1;this.unbind=Tt(document,"mousemove",function(i){r||(setTimeout(function(){var t=Date.now(),e=n.positions.length;e&&100<t-n.positions[e-1].time&&n.positions.splice(0,e),n.positions.push({time:t,x:i.pageX,y:i.pageY}),5<n.positions.length&&n.positions.shift(),r=!1},5),r=!0)})},cancel:function(){this.unbind&&this.unbind()},movesTo:function(t){if(this.positions.length<2)return!1;var e=Le(t),i=this.positions[this.positions.length-1],n=this.positions[0];if(e.left<=i.x&&i.x<=e.right&&e.top<=i.y&&i.y<=e.bottom)return!1;var r=[[{x:e.left,y:e.top},{x:e.right,y:e.bottom}],[{x:e.right,y:e.top},{x:e.left,y:e.bottom}]];return e.right<=i.x||(e.left>=i.x?(r[0].reverse(),r[1].reverse()):e.bottom<=i.y?r[0].reverse():e.top>=i.y&&r[1].reverse()),!!r.reduce(function(t,e){return t+(wi(n,e[0])<wi(i,e[0])&&wi(n,e[1])>wi(i,e[1]))},0)}};var bi={};function yi(t,e,i){return bi.computed(x(t)?t.call(i,i):t,x(e)?e.call(i,i):e)}bi.args=bi.events=bi.init=bi.created=bi.beforeConnect=bi.connected=bi.ready=bi.beforeDisconnect=bi.disconnected=bi.destroy=function(t,e){return t=t&&!y(t)?[t]:t,e?t?t.concat(e):y(e)?e:[e]:t},bi.update=function(t,e){return bi.args(t,x(e)?{read:e}:e)},bi.props=function(t,e){return y(e)&&(e=e.reduce(function(t,e){return t[e]=String,t},{})),bi.methods(t,e)},bi.computed=bi.methods=function(t,e){return e?t?Y({},t,e):e:t},bi.data=function(e,i,t){return t?yi(e,i,t):i?e?function(t){return yi(e,i,t)}:i:e};var xi=function(t,e){return D(e)?t:e};function ki(e,i,n){var r={};if(x(i)&&(i=i.options),i.extends&&(e=ki(e,i.extends,n)),i.mixins)for(var t=0,o=i.mixins.length;t<o;t++)e=ki(e,i.mixins[t],n);for(var s in e)l(s);for(var a in i)h(e,a)||l(a);function l(t){r[t]=(bi[t]||xi)(e[t],i[t],n)}return r}function $i(t,e){var i;void 0===e&&(e=[]);try{return t?v(t,"{")?JSON.parse(t):e.length&&!b(t,":")?((i={})[e[0]]=t,i):t.split(";").reduce(function(t,e){var i=e.split(/:(.*)/),n=i[0],r=i[1];return n&&!D(r)&&(t[n.trim()]=r.trim()),t},{}):{}}catch(t){return{}}}var Ii=0,Si=function(t){this.id=++Ii,this.el=H(t)};function Ti(t,e){try{t.contentWindow.postMessage(JSON.stringify(Y({event:"command"},e)),"*")}catch(t){}}Si.prototype.isVideo=function(){return this.isYoutube()||this.isVimeo()||this.isHTML5()},Si.prototype.isHTML5=function(){return"VIDEO"===this.el.tagName},Si.prototype.isIFrame=function(){return"IFRAME"===this.el.tagName},Si.prototype.isYoutube=function(){return this.isIFrame()&&!!this.el.src.match(/\/\/.*?youtube(-nocookie)?\.[a-z]+\/(watch\?v=[^&\s]+|embed)|youtu\.be\/.*/)},Si.prototype.isVimeo=function(){return this.isIFrame()&&!!this.el.src.match(/vimeo\.com\/video\/.*/)},Si.prototype.enableApi=function(){var e=this;if(this.ready)return this.ready;var i,r=this.isYoutube(),o=this.isVimeo();return r||o?this.ready=new zt(function(t){var n;Ct(e.el,"load",function(){if(r){var t=function(){return Ti(e.el,{event:"listening",id:e.id})};i=setInterval(t,100),t()}}),(n=function(t){return r&&t.id===e.id&&"onReady"===t.event||o&&Number(t.player_id)===e.id},new zt(function(i){Ct(window,"message",function(t,e){return i(e)},!1,function(t){var e=t.data;if(e&&N(e)){try{e=JSON.parse(e)}catch(t){return}return e&&n(e)}})})).then(function(){t(),i&&clearInterval(i)}),Z(e.el,"src",e.el.src+(b(e.el.src,"?")?"&":"?")+(r?"enablejsapi=1":"api=1&player_id="+e.id))}):zt.resolve()},Si.prototype.play=function(){var t=this;if(this.isVideo())if(this.isIFrame())this.enableApi().then(function(){return Ti(t.el,{func:"playVideo",method:"play"})});else if(this.isHTML5())try{var e=this.el.play();e&&e.catch(X)}catch(t){}},Si.prototype.pause=function(){var t=this;this.isVideo()&&(this.isIFrame()?this.enableApi().then(function(){return Ti(t.el,{func:"pauseVideo",method:"pause"})}):this.isHTML5()&&this.el.pause())},Si.prototype.mute=function(){var t=this;this.isVideo()&&(this.isIFrame()?this.enableApi().then(function(){return Ti(t.el,{func:"mute",method:"setVolume",value:0})}):this.isHTML5()&&(this.el.muted=!0,Z(this.el,"muted","")))};var Ei,Ci,_i,Ai,Ni={};function Oi(){Ei&&clearTimeout(Ei),Ci&&clearTimeout(Ci),_i&&clearTimeout(_i),Ei=Ci=_i=null,Ni={}}Rt(function(){Tt(document,"click",function(){return Ai=!0},!0),Tt(document,li,function(t){var e=t.target,i=Bi(t),n=i.x,r=i.y,o=Date.now(),s=zi(t.type);Ni.type&&Ni.type!==s||(Ni.el="tagName"in e?e:e.parentNode,Ei&&clearTimeout(Ei),Ni.x1=n,Ni.y1=r,Ni.last&&o-Ni.last<=250&&(Ni={}),Ni.type=s,Ni.last=o,Ai=0<t.button)}),Tt(document,hi,function(t){if(!t.defaultPrevented){var e=Bi(t),i=e.x,n=e.y;Ni.x2=i,Ni.y2=n}}),Tt(document,ci,function(t){var e=t.type,i=t.target;Ni.type===zi(e)&&(Ni.x2&&30<Math.abs(Ni.x1-Ni.x2)||Ni.y2&&30<Math.abs(Ni.y1-Ni.y2)?Ci=setTimeout(function(){var t,e,i,n,r;Ni.el&&(_t(Ni.el,"swipe"),_t(Ni.el,"swipe"+(e=(t=Ni).x1,i=t.x2,n=t.y1,r=t.y2,Math.abs(e-i)>=Math.abs(n-r)?0<e-i?"Left":"Right":0<n-r?"Up":"Down"))),Ni={}}):"last"in Ni?(_i=setTimeout(function(){return _t(Ni.el,"tap")}),Ni.el&&"mouseup"!==e&&St(i,Ni.el)&&(Ei=setTimeout(function(){Ei=null,Ni.el&&!Ai&&_t(Ni.el,"click"),Ni={}},350))):Ni={})}),Tt(document,"touchcancel",Oi),Tt(window,"scroll",Oi)});var Mi=!1;function Di(t){return Mi||"touch"===t.pointerType}function Bi(t){var e=t.touches,i=t.changedTouches,n=e&&e[0]||i&&i[0]||t;return{x:n.pageX,y:n.pageY}}function zi(t){return t.slice(0,5)}function Pi(t){return!(!v(t,"uk-")&&!v(t,"data-uk-"))&&g(t.replace("data-uk-","").replace("uk-",""))}Tt(document,"touchstart",function(){return Mi=!0},!0),Tt(document,"click",function(){Mi=!1}),Tt(document,"touchcancel",function(){return Mi=!1},!0);var Hi,Wi,Li,ji,Fi=function(t){this._init(t)};Fi.util=Object.freeze({ajax:Ft,getImage:Vt,transition:Ce,Transition:_e,animate:Oe,Animation:De,attr:Z,hasAttr:Q,removeAttr:tt,filterAttr:et,data:it,addClass:le,removeClass:he,removeClasses:ce,replaceClass:ue,hasClass:de,toggleClass:fe,$:Be,$$:ze,positionAt:We,offset:Le,position:Fe,height:Ve,width:Ye,flipPosition:Ke,isInView:Ge,scrolledOver:Ze,scrollTop:Qe,isReady:Yt,ready:Rt,index:qt,getIndex:Ut,empty:Xt,html:Jt,prepend:function(e,t){return(e=H(e)).hasChildNodes()?Qt(t,function(t){return e.insertBefore(t,e.firstChild)}):Kt(e,t)},append:Kt,before:Gt,after:Zt,remove:te,wrapAll:ee,wrapInner:ie,unwrap:ne,fragment:se,apply:ae,isIE:ni,isRtl:ri,hasTouch:ai,pointerDown:li,pointerMove:hi,pointerUp:ci,pointerEnter:ui,pointerLeave:di,on:Tt,off:Et,once:Ct,trigger:_t,createEvent:At,toEventTargets:Dt,preventClick:Bt,fastdom:fi,isVoidElement:yt,isVisible:xt,selInput:kt,isInput:$t,filter:It,within:St,bind:p,hasOwn:h,hyphenate:m,camelize:g,ucfirst:a,startsWith:v,endsWith:u,includes:b,isArray:y,isFunction:x,isObject:k,isPlainObject:$,isWindow:I,isDocument:S,isJQuery:T,isNode:E,isNodeCollection:_,isBoolean:A,isString:N,isNumber:O,isNumeric:M,isUndefined:D,toBoolean:B,toNumber:z,toFloat:P,toNode:H,toNodes:L,toList:j,toMs:F,swap:V,assign:Y,each:R,sortBy:q,clamp:U,noop:X,intersectRect:J,pointInRect:K,Dimensions:G,MouseTracker:vi,mergeOptions:ki,parseOptions:$i,Player:Si,Promise:zt,Deferred:Pt,query:nt,queryAll:rt,find:ot,findAll:st,matches:ft,closest:mt,parents:gt,escape:wt,css:be,getStyles:ye,getStyle:xe,getCssVar:$e,propName:Se,isTouch:Di,getPos:Bi}),Fi.data="__uikit__",Fi.prefix="uk-",Fi.options={},function(i){var e,n=i.data;function r(t,e){if(t)for(var i in t)t[i]._isReady&&t[i]._callUpdate(e)}i.use=function(t){if(!t.installed)return t.call(null,this),t.installed=!0,this},i.mixin=function(t,e){e=(N(e)?i.component(e):e)||this,(t=ki({},t)).mixins=e.options.mixins,delete e.options.mixins,e.options=ki(t,e.options)},i.extend=function(t){t=t||{};var e=function(t){this._init(t)};return((e.prototype=Object.create(this.prototype)).constructor=e).options=ki(this.options,t),e.super=this,e.extend=this.extend,e},i.update=function(t,e){e=At(e||"update"),function(t){for(var e=[];t&&t!==document.body&&t.parentNode;)t=t.parentNode,e.unshift(t);return e}(t=t?H(t):document.body).map(function(t){return r(t[n],e)}),ae(t,function(t){return r(t[n],e)})},Object.defineProperty(i,"container",{get:function(){return e||document.body},set:function(t){e=Be(t)}})}(Fi),(Hi=Fi).prototype._callHook=function(t){var e=this,i=this.$options[t];i&&i.forEach(function(t){return t.call(e)})},Hi.prototype._callConnected=function(){var t=this;this._connected||(this._data={},this._initProps(),this._callHook("beforeConnect"),this._connected=!0,this._initEvents(),this._initObserver(),this._callHook("connected"),this._isReady||Rt(function(){return t._callReady()}),this._callUpdate())},Hi.prototype._callDisconnected=function(){this._connected&&(this._callHook("beforeDisconnect"),this._observer&&(this._observer.disconnect(),this._observer=null),this._unbindEvents(),this._callHook("disconnected"),this._connected=!1)},Hi.prototype._callReady=function(){this._isReady||(this._isReady=!0,this._callHook("ready"),this._resetComputeds(),this._callUpdate())},Hi.prototype._callUpdate=function(o){var s=this,a=(o=At(o||"update")).type;b(["update","load","resize"],a)&&this._resetComputeds();var t=this.$options.update,e=this._frames,l=e.reads,h=e.writes;t&&t.forEach(function(t,e){var i=t.read,n=t.write,r=t.events;("update"===a||b(r,a))&&(i&&!b(fi.reads,l[e])&&(l[e]=fi.read(function(){var t=s._connected&&i.call(s,s._data,o);!1===t&&n?(fi.clear(h[e]),delete h[e]):$(t)&&Y(s._data,t),delete l[e]})),n&&!b(fi.writes,h[e])&&(h[e]=fi.write(function(){s._connected&&n.call(s,s._data,o),delete h[e]})))})},function(t){var e=0;function s(t,e){var i={},n=t.args;void 0===n&&(n=[]);var r=t.props;void 0===r&&(r={});var o=t.el;if(!r)return i;for(var s in r){var a=m(s),l=it(o,a);if(!D(l)){if(l=d(r[s],l),"target"===a&&(!l||v(l,"_")))continue;i[s]=l}}var h=$i(it(o,e),n);for(var c in h){var u=g(c);void 0!==r[u]&&(i[u]=d(r[u],h[c]))}return i}function i(n,r,o){Object.defineProperty(n,r,{enumerable:!0,get:function(){var t=n._computeds,e=n.$props,i=n.$el;return h(t,r)||(t[r]=o.call(n,e,i)),t[r]},set:function(t){n._computeds[r]=t}})}function f(e,i,n){$(i)||(i={name:n,handler:i});var r,o,t=i.name,s=i.el,a=i.handler,l=i.capture,h=i.passive,c=i.delegate,u=i.filter,d=i.self;s=x(s)?s.call(e):s||e.$el,y(s)?s.forEach(function(t){return f(e,Y({},i,{el:t}),n)}):!s||u&&!u.call(e)||(r=N(a)?e[a]:p(a,e),a=function(t){return y(t.detail)?r.apply(void 0,[t].concat(t.detail)):r(t)},d&&(o=a,a=function(t){if(t.target===t.currentTarget||t.target===t.current)return o.call(null,t)}),e._events.push(Tt(s,t,c?N(c)?c:c.call(e):null,a,A(h)?{passive:h,capture:l}:l)))}function n(t,e){return t.every(function(t){return!t||!h(t,e)})}function d(t,e){return t===Boolean?B(e):t===Number?z(e):"list"===t?j(e):"media"===t?function(t){if(N(t))if("@"===t[0]){var e="media-"+t.substr(1);t=P($e(e))}else if(isNaN(t))return t;return!(!t||isNaN(t))&&"(min-width: "+t+"px)"}(e):t?t(e):e}t.prototype._init=function(t){(t=t||{}).data=function(t,e){var i=t.data,n=(t.el,e.args),r=e.props;if(void 0===r&&(r={}),i=y(i)?n&&n.length?i.slice(0,n.length).reduce(function(t,e,i){return $(e)?Y(t,e):t[n[i]]=e,t},{}):void 0:i)for(var o in i)D(i[o])?delete i[o]:i[o]=r[o]?d(r[o],i[o]):i[o];return i}(t,this.constructor.options),this.$options=ki(this.constructor.options,t,this),this.$el=null,this.$props={},this._frames={reads:{},writes:{}},this._events=[],this._uid=e++,this._initData(),this._initMethods(),this._initComputeds(),this._callHook("created"),t.el&&this.$mount(t.el)},t.prototype._initData=function(){var t=this.$options.data;for(var e in void 0===t&&(t={}),t)this.$props[e]=this[e]=t[e]},t.prototype._initMethods=function(){var t=this.$options.methods;if(t)for(var e in t)this[e]=p(t[e],this)},t.prototype._initComputeds=function(){var t=this.$options.computed;if(this._resetComputeds(),t)for(var e in t)i(this,e,t[e])},t.prototype._resetComputeds=function(){this._computeds={}},t.prototype._initProps=function(t){var e;for(e in this._resetComputeds(),t=t||s(this.$options,this.$name))D(t[e])||(this.$props[e]=t[e]);var i=[this.$options.computed,this.$options.methods];for(e in this.$props)e in t&&n(i,e)&&(this[e]=this.$props[e])},t.prototype._initEvents=function(){var i=this,t=this.$options.events;t&&t.forEach(function(t){if(h(t,"handler"))f(i,t);else for(var e in t)f(i,t[e],e)})},t.prototype._unbindEvents=function(){this._events.forEach(function(t){return t()}),this._events=[]},t.prototype._initObserver=function(){var i=this,t=this.$options,n=t.attrs,e=t.props,r=t.el;if(!this._observer&&e&&n){n=y(n)?n:Object.keys(e),this._observer=new MutationObserver(function(){var e=s(i.$options,i.$name);n.some(function(t){return!D(e[t])&&e[t]!==i.$props[t]})&&i.$reset()});var o=n.map(function(t){return m(t)}).concat(this.$name);this._observer.observe(r,{attributes:!0,attributeFilter:o.concat(o.map(function(t){return"data-"+t}))})}}}(Fi),Li=(Wi=Fi).data,ji={},Wi.component=function(s,t){if(!t)return $(ji[s])&&(ji[s]=Wi.extend(ji[s])),ji[s];Wi[s]=function(t,i){for(var e=arguments.length,n=Array(e);e--;)n[e]=arguments[e];var r=Wi.component(s);return $(t)?new r({data:t}):r.options.functional?new r({data:[].concat(n)}):t&&t.nodeType?o(t):ze(t).map(o)[0];function o(t){var e=Wi.getComponent(t,s);if(e){if(!i)return e;e.$destroy()}return new r({el:t,data:i})}};var e=$(t)?Y({},t):t.options;if(e.name=s,e.install&&e.install(Wi,e,s),Wi._initialized&&!e.functional){var i=m(s);fi.read(function(){return Wi[s]("[uk-"+i+"],[data-uk-"+i+"]")})}return ji[s]=$(t)?e:t},Wi.getComponents=function(t){return t&&t[Li]||{}},Wi.getComponent=function(t,e){return Wi.getComponents(t)[e]},Wi.connect=function(t){if(t[Li])for(var e in t[Li])t[Li][e]._callConnected();for(var i=0;i<t.attributes.length;i++){var n=Pi(t.attributes[i].name);n&&n in ji&&Wi[n](t)}},Wi.disconnect=function(t){for(var e in t[Li])t[Li][e]._callDisconnected()},function(n){var r=n.data;n.prototype.$mount=function(t){var e=this.$options.name;t[r]||(t[r]={}),t[r][e]||((t[r][e]=this).$el=this.$options.el=this.$options.el||t,this._callHook("init"),St(t,document)&&this._callConnected())},n.prototype.$emit=function(t){this._callUpdate(t)},n.prototype.$reset=function(){this._callDisconnected(),this._callConnected()},n.prototype.$destroy=function(t){void 0===t&&(t=!1);var e=this.$options,i=e.el,n=e.name;i&&this._callDisconnected(),this._callHook("destroy"),i&&i[r]&&(delete i[r][n],Object.keys(i[r]).length||delete i[r],t&&te(this.$el))},n.prototype.$create=function(t,e,i){return n[t](e,i)},n.prototype.$update=n.update,n.prototype.$getComponent=n.getComponent;var e={};Object.defineProperties(n.prototype,{$container:Object.getOwnPropertyDescriptor(n,"container"),$name:{get:function(){var t=this.$options.name;return e[t]||(e[t]=n.prefix+m(t)),e[t]}}})}(Fi);var Vi={connected:function(){le(this.$el,this.$name)}},Yi={props:{cls:Boolean,animation:"list",duration:Number,origin:String,transition:String,queued:Boolean},data:{cls:!1,animation:[!1],duration:200,origin:!1,transition:"linear",queued:!1,initProps:{overflow:"",height:"",paddingTop:"",paddingBottom:"",marginTop:"",marginBottom:""},hideProps:{overflow:"hidden",height:0,paddingTop:0,paddingBottom:0,marginTop:0,marginBottom:0}},computed:{hasAnimation:function(t){return!!t.animation[0]},hasTransition:function(t){var e=t.animation;return this.hasAnimation&&!0===e[0]}},methods:{toggleElement:function(h,c,u){var d=this;return new zt(function(t){h=L(h);var e,i=function(t){return zt.all(t.map(function(t){return d._toggleElement(t,c,u)}))},n=h.filter(function(t){return d.isToggled(t)}),r=h.filter(function(t){return!b(n,t)});if(d.queued&&D(u)&&D(c)&&d.hasAnimation&&!(h.length<2)){var o=document.body,s=o.scrollTop,a=n[0],l=De.inProgress(a)&&de(a,"uk-animation-leave")||_e.inProgress(a)&&"0px"===a.style.height;e=i(n),l||(e=e.then(function(){var t=i(r);return o.scrollTop=s,t}))}else e=i(r.concat(n));e.then(t,X)})},toggleNow:function(e,i){var n=this;return new zt(function(t){return zt.all(L(e).map(function(t){return n._toggleElement(t,i,!1)})).then(t,X)})},isToggled:function(t){var e=L(t||this.$el);return this.cls?de(e,this.cls.split(" ")[0]):!Q(e,"hidden")},updateAria:function(t){!1===this.cls&&Z(t,"aria-hidden",!this.isToggled(t))},_toggleElement:function(t,e,i){var n=this;if(e=A(e)?e:De.inProgress(t)?de(t,"uk-animation-leave"):_e.inProgress(t)?"0px"===t.style.height:!this.isToggled(t),!_t(t,"before"+(e?"show":"hide"),[this]))return zt.reject();var r=(!1!==i&&this.hasAnimation?this.hasTransition?this._toggleHeight:this._toggleAnimation:this._toggleImmediate)(t,e);return _t(t,e?"show":"hide",[this]),r.then(function(){_t(t,e?"shown":"hidden",[n]),n.$update(t)})},_toggle:function(t,e){var i;t&&(this.cls?(i=b(this.cls," ")||Boolean(e)!==de(t,this.cls))&&fe(t,this.cls,b(this.cls," ")?void 0:e):(i=Boolean(e)===Q(t,"hidden"))&&Z(t,"hidden",e?null:""),ze("[autofocus]",t).some(function(t){return xt(t)&&(t.focus()||!0)}),this.updateAria(t),i&&this.$update(t))},_toggleImmediate:function(t,e){return this._toggle(t,e),zt.resolve()},_toggleHeight:function(t,e){var i=this,n=_e.inProgress(t),r=t.hasChildNodes?P(be(t.firstElementChild,"marginTop"))+P(be(t.lastElementChild,"marginBottom")):0,o=xt(t)?Ve(t)+(n?0:r):0;_e.cancel(t),this.isToggled(t)||this._toggle(t,!0),Ve(t,""),fi.flush();var s=Ve(t)+(n?0:r);return Ve(t,o),(e?_e.start(t,Y({},this.initProps,{overflow:"hidden",height:s}),Math.round(this.duration*(1-o/s)),this.transition):_e.start(t,this.hideProps,Math.round(this.duration*(o/s)),this.transition).then(function(){return i._toggle(t,!1)})).then(function(){return be(t,i.initProps)})},_toggleAnimation:function(t,e){var i=this;return De.cancel(t),e?(this._toggle(t,!0),De.in(t,this.animation[0],this.duration,this.origin)):De.out(t,this.animation[1]||this.animation[0],this.duration,this.origin).then(function(){return i._toggle(t,!1)})}}},Ri={mixins:[Vi,Yi],props:{targets:String,active:null,collapsible:Boolean,multiple:Boolean,toggle:String,content:String,transition:String},data:{targets:"> *",active:!1,animation:[!0],collapsible:!0,multiple:!1,clsOpen:"uk-open",toggle:"> .uk-accordion-title",content:"> .uk-accordion-content",transition:"ease"},computed:{items:function(t,e){return ze(t.targets,e)}},events:[{name:"click",delegate:function(){return this.targets+" "+this.$props.toggle},handler:function(t){t.preventDefault(),this.toggle(qt(ze(this.targets+" "+this.$props.toggle,this.$el),t.current))}}],connected:function(){if(!1!==this.active){var t=this.items[Number(this.active)];t&&!de(t,this.clsOpen)&&this.toggle(t,!1)}},update:function(){var e=this;this.items.forEach(function(t){return e._toggleImmediate(Be(e.content,t),de(t,e.clsOpen))});var t=!this.collapsible&&!de(this.items,this.clsOpen)&&this.items[0];t&&this.toggle(t,!1)},methods:{toggle:function(r,o){var s=this,t=Ut(r,this.items),a=It(this.items,"."+this.clsOpen);(r=this.items[t])&&[r].concat(!this.multiple&&!b(a,r)&&a||[]).forEach(function(t){var e=t===r,i=e&&!de(t,s.clsOpen);if(i||!e||s.collapsible||!(a.length<2)){fe(t,s.clsOpen,i);var n=t._wrapper?t._wrapper.firstElementChild:Be(s.content,t);t._wrapper||(t._wrapper=ee(n,"<div>"),Z(t._wrapper,"hidden",i?"":null)),s._toggleImmediate(n,!0),s.toggleElement(t._wrapper,i,o).then(function(){de(t,s.clsOpen)===i&&(i||s._toggleImmediate(n,!1),t._wrapper=null,ne(n))})}})}}},qi={mixins:[Vi,Yi],args:"animation",attrs:!0,props:{close:String},data:{animation:[!0],selClose:".uk-alert-close",duration:150,hideProps:Y({opacity:0},Yi.data.hideProps)},events:[{name:"click",delegate:function(){return this.selClose},handler:function(t){t.preventDefault(),this.close()}}],methods:{close:function(){var t=this;this.toggleElement(this.$el).then(function(){return t.$destroy(!0)})}}};function Ui(o){Rt(function(){var i=0,n=0;if(Tt(window,"load resize",function(t){return o.update(null,t)}),Tt(window,"scroll",function(t){var e=t.target;t.dir=i<=window.pageYOffset?"down":"up",t.pageYOffset=i=window.pageYOffset,o.update(1!==e.nodeType?document.body:e,t)},{passive:!0,capture:!0}),Tt(document,"loadedmetadata load",function(t){var e=t.target;return o.update(e,"load")},!0),Tt(document,"animationstart",function(t){var e=t.target;(be(e,"animationName")||"").match(/^uk-.*(left|right)/)&&(n++,be(document.body,"overflowX","hidden"),setTimeout(function(){--n||be(document.body,"overflowX","")},F(be(e,"animationDuration"))+100))},!0),ai){var r="uk-hover";Tt(document,"tap",function(t){var e=t.target;return ze("."+r).forEach(function(t){return!St(e,t)&&he(t,r)})}),Object.defineProperty(o,"hoverSelector",{set:function(t){Tt(document,"tap",t,function(t){return le(t.current,r)})}}),o.hoverSelector=".uk-animation-toggle, .uk-transition-toggle, [uk-hover]"}})}var Xi,Ji,Ki={args:"autoplay",props:{automute:Boolean,autoplay:Boolean},data:{automute:!1,autoplay:!0},computed:{inView:function(t){return"inview"===t.autoplay}},connected:function(){this.inView&&!Q(this.$el,"preload")&&(this.$el.preload="none")},ready:function(){this.player=new Si(this.$el),this.automute&&this.player.mute()},update:[{read:function(t,e){var i=e.type;return!(!this.player||!("scroll"!==i&&"resize"!==i||this.inView))&&{visible:xt(this.$el)&&"hidden"!==be(this.$el,"visibility"),inView:this.inView&&Ge(this.$el)}},write:function(t){var e=t.visible,i=t.inView;!e||this.inView&&!i?this.player.pause():(!0===this.autoplay||this.inView&&i)&&this.player.play()},events:["load","resize","scroll"]}]},Gi={mixins:[Vi,Ki],props:{width:Number,height:Number},data:{automute:!0},update:{read:function(){var t=this.$el;if(!xt(t))return!1;var e=t.parentNode;return{height:e.offsetHeight,width:e.offsetWidth}},write:function(t){var e=t.height,i=t.width,n=this.$el,r=this.width||n.naturalWidth||n.videoWidth||n.clientWidth,o=this.height||n.naturalHeight||n.videoHeight||n.clientHeight;r&&o&&be(n,G.cover({width:r,height:o},{width:i+(i%2?1:0),height:e+(e%2?1:0)}))},events:["load","resize"]}},Zi={props:{pos:String,offset:null,flip:Boolean,clsPos:String},data:{pos:"bottom-"+(ri?"right":"left"),flip:!0,offset:!1,clsPos:""},computed:{pos:function(t){var e=t.pos;return(e+(b(e,"-")?"":"-center")).split("-")},dir:function(){return this.pos[0]},align:function(){return this.pos[1]}},methods:{positionAt:function(t,e,i){var n;ce(t,this.clsPos+"-(top|bottom|left|right)(-[a-z]+)?"),be(t,{top:"",left:""});var r=this.offset;r=M(r)?r:(n=Be(r))?Le(n)["x"===o?"left":"top"]-Le(e)["x"===o?"right":"bottom"]:0;var o=this.getAxis(),s=We(t,e,"x"===o?Ke(this.dir)+" "+this.align:this.align+" "+Ke(this.dir),"x"===o?this.dir+" "+this.align:this.align+" "+this.dir,"x"===o?""+("left"===this.dir?-r:r):" "+("top"===this.dir?-r:r),null,this.flip,i).target,a=s.x,l=s.y;this.dir="x"===o?a:l,this.align="x"===o?l:a,fe(t,this.clsPos+"-"+this.dir+"-"+this.align,!1===this.offset)},getAxis:function(){return"top"===this.dir||"bottom"===this.dir?"y":"x"}}},Qi={mixins:[Zi,Yi],args:"pos",props:{mode:"list",toggle:Boolean,boundary:Boolean,boundaryAlign:Boolean,delayShow:Number,delayHide:Number,clsDrop:String},data:{mode:["click","hover"],toggle:"- *",boundary:window,boundaryAlign:!1,delayShow:0,delayHide:800,clsDrop:!1,hoverIdle:200,animation:["uk-animation-fade"],cls:"uk-open"},computed:{boundary:function(t,e){return nt(t.boundary,e)},clsDrop:function(t){return t.clsDrop||"uk-"+this.$options.name},clsPos:function(){return this.clsDrop}},init:function(){this.tracker=new vi},connected:function(){le(this.$el,this.clsDrop);var t=this.$props.toggle;this.toggle=t&&this.$create("toggle",nt(t,this.$el),{target:this.$el,mode:this.mode}),this.updateAria(this.$el)},events:[{name:"click",delegate:function(){return"."+this.clsDrop+"-close"},handler:function(t){t.preventDefault(),this.hide(!1)}},{name:"click",delegate:function(){return'a[href^="#"]'},handler:function(t){if(!t.defaultPrevented){var e=t.target.hash;e||t.preventDefault(),e&&St(e,this.$el)||this.hide(!1)}}},{name:"beforescroll",handler:function(){this.hide(!1)}},{name:"toggle",self:!0,handler:function(t,e){t.preventDefault(),this.isToggled()?this.hide(!1):this.show(e,!1)}},{name:ui,filter:function(){return b(this.mode,"hover")},handler:function(t){Di(t)||(Xi&&Xi!==this&&Xi.toggle&&b(Xi.toggle.mode,"hover")&&!St(t.target,Xi.toggle.$el)&&!K({x:t.pageX,y:t.pageY},Le(Xi.$el))&&Xi.hide(!1),t.preventDefault(),this.show(this.toggle))}},{name:"toggleshow",handler:function(t,e){e&&!b(e.target,this.$el)||(t.preventDefault(),this.show(e||this.toggle))}},{name:"togglehide "+di,handler:function(t,e){Di(t)||e&&!b(e.target,this.$el)||(t.preventDefault(),this.toggle&&b(this.toggle.mode,"hover")&&this.hide())}},{name:"beforeshow",self:!0,handler:function(){this.clearTimers(),De.cancel(this.$el),this.position()}},{name:"show",self:!0,handler:function(){this.tracker.init(),this.toggle&&(le(this.toggle.$el,this.cls),Z(this.toggle.$el,"aria-expanded","true")),function(){if(Ji)return;Ji=!0,Tt(document,"click",function(t){var e,i=t.target,n=t.defaultPrevented;if(!n)for(;Xi&&Xi!==e&&!St(i,Xi.$el)&&(!Xi.toggle||!St(i,Xi.toggle.$el));)(e=Xi).hide(!1)})}()}},{name:"beforehide",self:!0,handler:function(){this.clearTimers()}},{name:"hide",handler:function(t){var e=t.target;this.$el===e?(Xi=this.isActive()?null:Xi,this.toggle&&(he(this.toggle.$el,this.cls),Z(this.toggle.$el,"aria-expanded","false"),this.toggle.$el.blur(),ze("a, button",this.toggle.$el).forEach(function(t){return t.blur()})),this.tracker.cancel()):Xi=null===Xi&&St(e,this.$el)&&this.isToggled()?this:Xi}}],update:{write:function(){this.isToggled()&&!De.inProgress(this.$el)&&this.position()},events:["resize"]},methods:{show:function(e,i){var n=this;void 0===i&&(i=!0);var r=function(){return!n.isToggled()&&n.toggleElement(n.$el,!0)},t=function(){if(n.toggle=e||n.toggle,n.clearTimers(),!n.isActive())if(i&&Xi&&Xi!==n&&Xi.isDelaying)n.showTimer=setTimeout(n.show,10);else{if(n.isParentOf(Xi)){if(!Xi.hideTimer)return;Xi.hide(!1)}else if(Xi&&!n.isChildOf(Xi)&&!n.isParentOf(Xi))for(var t;Xi&&Xi!==t&&!n.isChildOf(Xi);)(t=Xi).hide(!1);i&&n.delayShow?n.showTimer=setTimeout(r,n.delayShow):r(),Xi=n}};e&&this.toggle&&e.$el!==this.toggle.$el?(Ct(this.$el,"hide",t),this.hide(!1)):t()},hide:function(t){var e=this;void 0===t&&(t=!0);var i=function(){return e.toggleNow(e.$el,!1)};this.clearTimers(),this.isDelaying=this.tracker.movesTo(this.$el),t&&this.isDelaying?this.hideTimer=setTimeout(this.hide,this.hoverIdle):t&&this.delayHide?this.hideTimer=setTimeout(i,this.delayHide):i()},clearTimers:function(){clearTimeout(this.showTimer),clearTimeout(this.hideTimer),this.showTimer=null,this.hideTimer=null,this.isDelaying=!1},isActive:function(){return Xi===this},isChildOf:function(t){return t&&t!==this&&St(this.$el,t.$el)},isParentOf:function(t){return t&&t!==this&&St(t.$el,this.$el)},position:function(){ce(this.$el,this.clsDrop+"-(stack|boundary)"),be(this.$el,{top:"",left:"",display:"block"}),fe(this.$el,this.clsDrop+"-boundary",this.boundaryAlign);var t=Le(this.boundary),e=this.boundaryAlign?t:Le(this.toggle.$el);if("justify"===this.align){var i="y"===this.getAxis()?"width":"height";be(this.$el,i,e[i])}else this.$el.offsetWidth>Math.max(t.right-e.left,e.right-t.left)&&le(this.$el,this.clsDrop+"-stack");this.positionAt(this.$el,this.boundaryAlign?this.boundary:this.toggle.$el,this.boundary),be(this.$el,"display","")}}};var tn={extends:Qi},en={mixins:[Vi],args:"target",props:{target:Boolean},data:{target:!1},computed:{input:function(t,e){return Be(kt,e)},state:function(){return this.input.nextElementSibling},target:function(t,e){var i=t.target;return i&&(!0===i&&this.input.parentNode===e&&this.input.nextElementSibling||nt(i,e))}},update:function(){var t=this.target,e=this.input;if(t){var i,n=$t(t)?"value":"textContent",r=t[n],o=e.files&&e.files[0]?e.files[0].name:ft(e,"select")&&(i=ze("option",e).filter(function(t){return t.selected})[0])?i.textContent:e.value;r!==o&&(t[n]=o)}},events:[{name:"focusin focusout mouseenter mouseleave",delegate:kt,handler:function(t){var e=t.type;t.current===this.input&&fe(this.state,"uk-"+(b(e,"focus")?"focus":"hover"),b(["focusin","mouseenter"],e))}},{name:"change",handler:function(){this.$emit()}}]},nn={update:{read:function(t){var e=Ge(this.$el);if(!e||t.isInView===e)return!1;t.isInView=e},write:function(){this.$el.src=this.$el.src},events:["scroll","load","resize"]}},rn={props:{margin:String,firstColumn:Boolean},data:{margin:"uk-margin-small-top",firstColumn:"uk-first-column"},update:{read:function(t){var e=this.$el.children;if(!e.length||!xt(this.$el))return t.rows=[[]];t.rows=on(e),t.stacks=!t.rows.some(function(t){return 1<t.length})},write:function(t){var n=this;t.rows.forEach(function(t,i){return t.forEach(function(t,e){fe(t,n.margin,0!==i),fe(t,n.firstColumn,0===e)})})},events:["load","resize"]}};function on(t){for(var e=[[]],i=0;i<t.length;i++){var n=t[i],r=sn(n);if(r.height)for(var o=e.length-1;0<=o;o--){var s=e[o];if(!s[0]){s.push(n);break}var a=sn(s[0]);if(r.top>=a.bottom-1){e.push([n]);break}if(r.bottom>a.top){if(r.left<a.left&&!ri){s.unshift(n);break}s.push(n);break}if(0===o){e.unshift([n]);break}}}return e}function sn(t){var e=t.offsetTop,i=t.offsetLeft,n=t.offsetHeight;return{top:e,left:i,height:n,bottom:e+n}}var an={extends:rn,mixins:[Vi],attrs:!0,name:"grid",props:{masonry:Boolean,parallax:Number},data:{margin:"uk-grid-margin",clsStack:"uk-grid-stack",masonry:!1,parallax:0},computed:{length:function(t,e){return e.children.length},parallax:function(t){var e=t.parallax;return e&&this.length?Math.abs(e):""}},connected:function(){this.masonry&&le(this.$el,"uk-flex-top uk-flex-wrap-top")},update:[{read:function(t){var r=t.rows;(this.masonry||this.parallax)&&(r=r.map(function(t){return q(t,"offsetLeft")}));var e,i,n,o,s,a=r.some(function(t){return t.some(function(t){return"static"===be(t,"position")})}),l=!1,h="";if(this.masonry&&this.length){var c=0;l=r.reduce(function(i,t,n){return i[n]=t.map(function(t,e){return 0===n?0:P(i[n-1][e])+(c-P(r[n-1][e]&&r[n-1][e].offsetHeight))}),c=t.reduce(function(t,e){return Math.max(t,e.offsetHeight)},0),i},[]),s=r,h=Math.max.apply(Math,s.reduce(function(i,t){return t.forEach(function(t,e){return i[e]=(i[e]||0)+t.offsetHeight}),i},[]))+(e=this.$el,i=this.margin,n=L(e.children),P((o=n.filter(function(t){return de(t,i)})[0])?be(o,"marginTop"):be(n[0],"paddingLeft"))*(r.length-1))}return{rows:r,translates:l,height:!!a&&h}},write:function(t){var e=t.stacks,i=t.height;fe(this.$el,this.clsStack,e),be(this.$el,"paddingBottom",this.parallax),!1!==i&&be(this.$el,"height",i)},events:["load","resize"]},{read:function(t){var e=t.height;return{scrolled:!!this.parallax&&Ze(this.$el,e?e-Ve(this.$el):0)*this.parallax}},write:function(t){var e=t.rows,n=t.scrolled,r=t.translates;(!1!==n||r)&&e.forEach(function(t,i){return t.forEach(function(t,e){return be(t,"transform",n||r?"translateY("+((r&&-r[i][e])+(n?e%2?n:n/8:0))+"px)":"")})})},events:["scroll","load","resize"]}]};var ln={args:"target",props:{target:String,row:Boolean},data:{target:"> *",row:!0},computed:{elements:function(t,e){return ze(t.target,e)}},update:{read:function(){var e=this;return be(this.elements,{minHeight:"",boxSizing:""}),{rows:this.row?on(this.elements).map(function(t){return e.match(t)}):[this.match(this.elements)]}},write:function(t){t.rows.forEach(function(t){var e=t.height;return be(t.elements,{minHeight:e,boxSizing:"border-box"})})},events:["load","resize"]},methods:{match:function(t){if(t.length<2)return{};var i=[],n=0;return t.forEach(function(t){var e=Le(t).height;n=Math.max(n,e),i.push(e)}),t=t.filter(function(t,e){return i[e]<n}),{height:n,elements:t}}}},hn={props:{expand:Boolean,offsetTop:Boolean,offsetBottom:Boolean,minHeight:Number},data:{expand:!1,offsetTop:!1,offsetBottom:!1,minHeight:0},connected:function(){be(this.$el,"boxSizing","border-box")},update:{read:function(){var t=Ve(window),e="";if(this.expand)e=t-(cn(document.documentElement)-cn(this.$el))||"";else{var i=Le(this.$el).top;e="calc(100vh",i<t/2&&this.offsetTop&&(e+=" - "+i+"px"),!0===this.offsetBottom?e+=" - "+cn(this.$el.nextElementSibling)+"px":M(this.offsetBottom)?e+=" - "+this.offsetBottom+"vh":this.offsetBottom&&u(this.offsetBottom,"px")?e+=" - "+P(this.offsetBottom)+"px":N(this.offsetBottom)&&(e+=" - "+cn(nt(this.offsetBottom,this.$el))+"px"),e+=")"}return{minHeight:e,viewport:t}},write:function(t){var e,i=t.minHeight;be(this.$el,{height:"",minHeight:i}),this.minHeight&&P(be(this.$el,"minHeight"))<this.minHeight&&be(this.$el,"minHeight",this.minHeight),(e=Math.round(P(be(this.$el,"minHeight"))))>=cn(this.$el)&&be(this.$el,"height",e)},events:["load","resize"]}};function cn(t){return t&&t.offsetHeight||0}var un={},dn={attrs:!0,props:{id:String,icon:String,src:String,style:String,width:Number,height:Number,ratio:Number,class:String},data:{ratio:1,id:!1,exclude:["ratio","src","icon"],class:""},connected:function(){var t,a=this;if(this.class+=" uk-svg",!this.icon&&b(this.src,"#")){var e=this.src.split("#");1<e.length&&(t=e,this.src=t[0],this.icon=t[1])}this.svg=this.getSvg().then(function(t){var e;if(N(t)?(a.icon&&b(t,"<symbol")&&(t=function(t,e){if(!pn[t]){var i;for(pn[t]={};i=fn.exec(t);)pn[t][i[3]]='<svg xmlns="http://www.w3.org/2000/svg"'+i[1]+"svg>";fn.lastIndex=0}return pn[t][e]}(t,a.icon)||t),e=Be(t.substr(t.indexOf("<svg")))):e=t.cloneNode(!0),!e)return zt.reject("SVG not found.");var i=Z(e,"viewBox");for(var n in i&&(i=i.split(" "),a.width=a.$props.width||i[2],a.height=a.$props.height||i[3]),a.width*=a.ratio,a.height*=a.ratio,a.$options.props)a[n]&&!b(a.exclude,n)&&Z(e,n,a[n]);a.id||tt(e,"id"),a.width&&!a.height&&tt(e,"height"),a.height&&!a.width&&tt(e,"width");var r=a.$el;if(yt(r)||"CANVAS"===r.tagName){Z(r,{hidden:!0,id:null});var o=r.nextElementSibling;o&&e.isEqualNode(o)?e=o:Zt(r,e)}else{var s=r.lastElementChild;s&&e.isEqualNode(s)?e=s:Kt(r,e)}return a.svgEl=e},X)},disconnected:function(){var e=this;yt(this.$el)&&Z(this.$el,{hidden:null,id:this.id||null}),this.svg&&this.svg.then(function(t){return(!e._connected||t!==e.svgEl)&&te(t)},X),this.svg=this.svgEl=null},methods:{getSvg:function(){var i=this;return this.src?(un[this.src]||(un[this.src]=new zt(function(e,t){v(i.src,"data:")?e(decodeURIComponent(i.src.split(",")[1])):Ft(i.src).then(function(t){return e(t.response)},function(){return t("SVG not found.")})})),un[this.src]):zt.reject()}}},fn=/<symbol(.*?id=(['"])(.*?)\2[^]*?<\/)symbol>/g,pn={};var mn={},gn={spinner:'<svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg"><circle fill="none" stroke="#000" cx="15" cy="15" r="14"/></svg>',totop:'<svg width="18" height="10" viewBox="0 0 18 10" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" stroke="#000" stroke-width="1.2" points="1 9 9 1 17 9 "/></svg>',marker:'<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><rect x="9" y="4" width="1" height="11"/><rect x="4" y="9" width="11" height="1"/></svg>',"close-icon":'<svg width="14" height="14" viewBox="0 0 14 14" xmlns="http://www.w3.org/2000/svg"><line fill="none" stroke="#000" stroke-width="1.1" x1="1" y1="1" x2="13" y2="13"/><line fill="none" stroke="#000" stroke-width="1.1" x1="13" y1="1" x2="1" y2="13"/></svg>',"close-large":'<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><line fill="none" stroke="#000" stroke-width="1.4" x1="1" y1="1" x2="19" y2="19"/><line fill="none" stroke="#000" stroke-width="1.4" x1="19" y1="1" x2="1" y2="19"/></svg>',"navbar-toggle-icon":'<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><rect y="9" width="20" height="2"/><rect y="3" width="20" height="2"/><rect y="15" width="20" height="2"/></svg>',"overlay-icon":'<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><rect x="19" y="0" width="1" height="40"/><rect x="0" y="19" width="40" height="1"/></svg>',"pagination-next":'<svg width="7" height="12" viewBox="0 0 7 12" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" stroke="#000" stroke-width="1.2" points="1 1 6 6 1 11"/></svg>',"pagination-previous":'<svg width="7" height="12" viewBox="0 0 7 12" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" stroke="#000" stroke-width="1.2" points="6 1 1 6 6 11"/></svg>',"search-icon":'<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><circle fill="none" stroke="#000" stroke-width="1.1" cx="9" cy="9" r="7"/><path fill="none" stroke="#000" stroke-width="1.1" d="M14,14 L18,18 L14,14 Z"/></svg>',"search-large":'<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><circle fill="none" stroke="#000" stroke-width="1.8" cx="17.5" cy="17.5" r="16.5"/><line fill="none" stroke="#000" stroke-width="1.8" x1="38" y1="39" x2="29" y2="30"/></svg>',"search-navbar":'<svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><circle fill="none" stroke="#000" stroke-width="1.1" cx="10.5" cy="10.5" r="9.5"/><line fill="none" stroke="#000" stroke-width="1.1" x1="23" y1="23" x2="17" y2="17"/></svg>',"slidenav-next":'<svg width="14px" height="24px" viewBox="0 0 14 24" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" stroke="#000" stroke-width="1.4" points="1.225,23 12.775,12 1.225,1 "/></svg>',"slidenav-next-large":'<svg width="25px" height="40px" viewBox="0 0 25 40" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" stroke="#000" stroke-width="2" points="4.002,38.547 22.527,20.024 4,1.5 "/></svg>',"slidenav-previous":'<svg width="14px" height="24px" viewBox="0 0 14 24" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" stroke="#000" stroke-width="1.4" points="12.775,1 1.225,12 12.775,23 "/></svg>',"slidenav-previous-large":'<svg width="25px" height="40px" viewBox="0 0 25 40" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" stroke="#000" stroke-width="2" points="20.527,1.5 2,20.024 20.525,38.547 "/></svg>'},vn={install:function(r){r.icon.add=function(t,e){var i,n=N(t)?((i={})[t]=e,i):t;R(n,function(t,e){gn[e]=t,delete mn[e]}),r._initialized&&ae(document.body,function(t){return R(r.getComponents(t),function(t){return t.$options.isIcon&&t.icon in n&&t.$reset()})})}},attrs:["icon","ratio"],mixins:[Vi,dn],args:"icon",props:["icon"],data:{exclude:["id","style","class","src","icon","ratio"]},isIcon:!0,connected:function(){le(this.$el,"uk-icon")},methods:{getSvg:function(){var t,e=function(t){if(!gn[t])return null;mn[t]||(mn[t]=Be(gn[t].trim()));return mn[t]}((t=this.icon,ri?V(V(t,"left","right"),"previous","next"):t));return e?zt.resolve(e):zt.reject("Icon not found.")}}},wn={extends:vn,data:function(t){return{icon:m(t.constructor.options.name)}}},bn={extends:wn,connected:function(){le(this.$el,"uk-slidenav")},computed:{icon:function(t,e){var i=t.icon;return de(e,"uk-slidenav-large")?i+"-large":i}}},yn={extends:wn,computed:{icon:function(t,e){var i=t.icon;return de(e,"uk-search-icon")&&gt(e,".uk-search-large").length?"search-large":gt(e,".uk-search-navbar").length?"search-navbar":i}}},xn={extends:wn,computed:{icon:function(){return"close-"+(de(this.$el,"uk-close-large")?"large":"icon")}}},kn={extends:wn,connected:function(){var e=this;this.svg.then(function(t){return 1!==e.ratio&&be(Be("circle",t),"strokeWidth",1/e.ratio)},X)}};var $n={attrs:!0,props:{dataSrc:String,dataSrcset:Boolean,sizes:String,width:Number,height:Number,offsetTop:String,offsetLeft:String,target:String},data:{dataSrc:"",dataSrcset:!1,sizes:!1,width:!1,height:!1,offsetTop:"50vh",offsetLeft:0,target:!1},computed:{cacheKey:function(t){var e=t.dataSrc;return this.$name+"."+e},width:function(t){var e=t.width,i=t.dataWidth;return e||i},height:function(t){var e=t.height,i=t.dataHeight;return e||i},sizes:function(t){var e=t.sizes,i=t.dataSizes;return e||i},isImg:function(t,e){return Nn(e)},target:function(t){var e=t.target;return[this.$el].concat(rt(e,this.$el))},offsetTop:function(t){return Cn(t.offsetTop,"height")},offsetLeft:function(t){return Cn(t.offsetLeft,"width")}},connected:function(){Mn[this.cacheKey]?In(this.$el,Mn[this.cacheKey]||this.dataSrc,this.dataSrcset,this.sizes):this.isImg&&this.width&&this.height&&In(this.$el,function(t,e,i){var n;if(i){for(var r;r=Sn.exec(i);)if(!r[1]||window.matchMedia(r[1]).matches){o=r[2],r=v(o,"calc")?o.substring(5,o.length-1).replace(Tn,function(t){return Cn(t)}).replace(/ /g,"").match(En).reduce(function(t,e){return t+ +e},0):o;break}Sn.lastIndex=0,n=G.ratio({width:t,height:e},"width",Cn(r||"100vw")),t=n.width,e=n.height}var o;return'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="'+t+'" height="'+e+'"></svg>'}(this.width,this.height,this.sizes))},update:[{read:function(t){var e=this,i=t.delay,n=t.image;if(i){if(!n&&this.target.some(function(t){return Ge(t,e.offsetTop,e.offsetLeft,!0)}))return{image:Vt(this.dataSrc,this.dataSrcset,this.sizes).then(function(t){return In(e.$el,On(t),t.srcset,t.sizes),Mn[e.cacheKey]=On(t),t},X)};!this.isImg&&n&&n.then(function(t){return t&&In(e.$el,On(t))})}},write:function(t){if(!t.delay)return this.$emit(),t.delay=!0},events:["scroll","load","resize"]}]};function In(t,e,i,n){if(Nn(t))e&&(t.src=e),i&&(t.srcset=i),n&&(t.sizes=n);else if(e){var r=!b(t.style.backgroundImage,e);be(t,"backgroundImage","url("+e+")"),r&&_t(t,At("load",!1))}}var Sn=/\s*(.*?)\s*(\w+|calc\(.*?\))\s*(?:,|$)/g;var Tn=/\d+(?:\w+|%)/g,En=/[+-]?(\d+)/g;function Cn(t,e,i){return void 0===e&&(e="width"),void 0===i&&(i=window),M(t)?+t:u(t,"vw")?An(i,"width",t):u(t,"vh")?An(i,"height",t):u(t,"%")?An(i,e,t):P(t)}var _n={height:Ve,width:Ye};function An(t,e,i){return _n[e](t)*P(i)/100}function Nn(t){return"IMG"===t.tagName}function On(t){return t.currentSrc||t.src}var Mn,Dn="__test__";try{(Mn=window.sessionStorage||{})[Dn]=1,delete Mn[Dn]}catch(t){Mn={}}var Bn,zn,Pn={mixins:[Vi],props:{fill:String,media:"media"},data:{fill:"",media:!1,clsWrapper:"uk-leader-fill",clsHide:"uk-leader-hide",attrFill:"data-fill"},computed:{fill:function(t){return t.fill||$e("leader-fill")}},connected:function(){var t;t=ie(this.$el,'<span class="'+this.clsWrapper+'">'),this.wrapper=t[0]},disconnected:function(){ne(this.wrapper.childNodes)},update:[{read:function(t){var e=t.changed,i=t.width,n=i;return{width:i=Math.floor(this.$el.offsetWidth/2),changed:e||n!==i,hide:this.media&&!window.matchMedia(this.media).matches}},write:function(t){fe(this.wrapper,this.clsHide,t.hide),t.changed&&(t.changed=!1,Z(this.wrapper,this.attrFill,new Array(t.width).join(this.fill)))},events:["load","resize"]}]},Hn={props:{container:Boolean},data:{container:!0},computed:{container:function(t){var e=t.container;return!0===e&&this.$container||e&&Be(e)}}},Wn={mixins:[Vi,Hn,Yi],props:{selPanel:String,selClose:String,escClose:Boolean,bgClose:Boolean,stack:Boolean},data:{cls:"uk-open",escClose:!0,bgClose:!0,overlay:!0,stack:!1},computed:{panel:function(t,e){return Be(t.selPanel,e)},transitionElement:function(){return this.panel},transitionDuration:function(){return F(be(this.transitionElement,"transitionDuration"))},bgClose:function(t){return t.bgClose&&this.panel}},events:[{name:"click",delegate:function(){return this.selClose},handler:function(t){t.preventDefault(),this.hide()}},{name:"toggle",self:!0,handler:function(t){t.defaultPrevented||(t.preventDefault(),this.toggle())}},{name:"beforeshow",self:!0,handler:function(t){var e=Bn&&Bn!==this&&Bn;if(Bn=this,e){if(!this.stack)return e.hide().then(this.show),void t.preventDefault();this.prev=e}!function(){if(zn)return;zn=[Tt(document,"click",function(t){var e=t.target,i=t.defaultPrevented;!Bn||!Bn.bgClose||i||Bn.overlay&&!St(e,Bn.$el)||St(e,Bn.panel)||Bn.hide()}),Tt(document,"keydown",function(t){27===t.keyCode&&Bn&&Bn.escClose&&(t.preventDefault(),Bn.hide())})]}()}},{name:"beforehide",self:!0,handler:function(){(Bn=Bn&&Bn!==this&&Bn||this.prev)||(zn&&zn.forEach(function(t){return t()}),zn=null)}},{name:"show",self:!0,handler:function(){de(document.documentElement,this.clsPage)||(this.scrollbarWidth=Ye(window)-Ye(document),be(document.body,"overflowY",this.scrollbarWidth&&this.overlay?"scroll":"")),le(document.documentElement,this.clsPage)}},{name:"hidden",self:!0,handler:function(){for(var t,e=this.prev;e;){if(e.clsPage===this.clsPage){t=!0;break}e=e.prev}t||he(document.documentElement,this.clsPage),!this.prev&&be(document.body,"overflowY","")}}],methods:{toggle:function(){return this.isToggled()?this.hide():this.show()},show:function(){return this.isToggled()?zt.resolve():(this.container&&this.$el.parentNode!==this.container&&(Kt(this.container,this.$el),this._callConnected()),this.toggleNow(this.$el,!0))},hide:function(){return this.isToggled()?this.toggleNow(this.$el,!1):zt.resolve()},getActive:function(){return Bn},_toggleImmediate:function(e,i){var n=this;return new zt(function(t){return requestAnimationFrame(function(){n._toggle(e,i),n.transitionDuration?Ct(n.transitionElement,"transitionend",t,!1,function(t){return t.target===n.transitionElement}):t()})})}}};var Ln={install:function(a){a.modal.dialog=function(t,e){var n=a.modal(' <div class="uk-modal"> <div class="uk-modal-dialog">'+t+"</div> </div> ",e);return n.show(),Tt(n.$el,"hidden",function(t){var e=t.target,i=t.currentTarget;e===i&&n.$destroy(!0)}),n},a.modal.alert=function(e,i){return i=Y({bgClose:!1,escClose:!1,labels:a.modal.labels},i),new zt(function(t){return Tt(a.modal.dialog(' <div class="uk-modal-body">'+(N(e)?e:Jt(e))+'</div> <div class="uk-modal-footer uk-text-right"> <button class="uk-button uk-button-primary uk-modal-close" autofocus>'+i.labels.ok+"</button> </div> ",i).$el,"hide",t)})},a.modal.confirm=function(r,o){return o=Y({bgClose:!1,escClose:!0,labels:a.modal.labels},o),new zt(function(e,t){var i=a.modal.dialog(' <form> <div class="uk-modal-body">'+(N(r)?r:Jt(r))+'</div> <div class="uk-modal-footer uk-text-right"> <button class="uk-button uk-button-default uk-modal-close" type="button">'+o.labels.cancel+'</button> <button class="uk-button uk-button-primary" autofocus>'+o.labels.ok+"</button> </div> </form> ",o),n=!1;Tt(i.$el,"submit","form",function(t){t.preventDefault(),e(),n=!0,i.hide()}),Tt(i.$el,"hide",function(){n||t()})})},a.modal.prompt=function(t,o,s){return s=Y({bgClose:!1,escClose:!0,labels:a.modal.labels},s),new zt(function(e){var i=a.modal.dialog(' <form class="uk-form-stacked"> <div class="uk-modal-body"> <label>'+(N(t)?t:Jt(t))+'</label> <input class="uk-input" autofocus> </div> <div class="uk-modal-footer uk-text-right"> <button class="uk-button uk-button-default uk-modal-close" type="button">'+s.labels.cancel+'</button> <button class="uk-button uk-button-primary">'+s.labels.ok+"</button> </div> </form> ",s),n=Be("input",i.$el);n.value=o;var r=!1;Tt(i.$el,"submit","form",function(t){t.preventDefault(),e(n.value),r=!0,i.hide()}),Tt(i.$el,"hide",function(){r||e(null)})})},a.modal.labels={ok:"Ok",cancel:"Cancel"}},mixins:[Wn],data:{clsPage:"uk-modal-page",selPanel:".uk-modal-dialog",selClose:".uk-modal-close, .uk-modal-close-default, .uk-modal-close-outside, .uk-modal-close-full"},events:[{name:"show",self:!0,handler:function(){de(this.panel,"uk-margin-auto-vertical")?le(this.$el,"uk-flex"):be(this.$el,"display","block"),Ve(this.$el)}},{name:"hidden",self:!0,handler:function(){be(this.$el,"display",""),he(this.$el,"uk-flex")}}]};var jn,Fn={extends:Ri,data:{targets:"> .uk-parent",toggle:"> a",content:"> ul"}},Vn={mixins:[Vi],props:{dropdown:String,mode:"list",align:String,offset:Number,boundary:Boolean,boundaryAlign:Boolean,clsDrop:String,delayShow:Number,delayHide:Number,dropbar:Boolean,dropbarMode:String,dropbarAnchor:Boolean,duration:Number},data:{dropdown:".uk-navbar-nav > li",align:ri?"right":"left",clsDrop:"uk-navbar-dropdown",mode:void 0,offset:void 0,delayShow:void 0,delayHide:void 0,boundaryAlign:void 0,flip:"x",boundary:!0,dropbar:!1,dropbarMode:"slide",dropbarAnchor:!1,duration:200},computed:{boundary:function(t,e){var i=t.boundary,n=t.boundaryAlign;return!0===i||n?e:i},dropbarAnchor:function(t,e){return nt(t.dropbarAnchor,e)},pos:function(t){return"bottom-"+t.align},dropdowns:function(t,e){return ze(t.dropdown+" ."+t.clsDrop,e)}},beforeConnect:function(){var t=this.$props.dropbar;this.dropbar=t&&(nt(t,this.$el)||Be("+ .uk-navbar-dropbar",this.$el)||Be("<div></div>")),this.dropbar&&(le(this.dropbar,"uk-navbar-dropbar"),"slide"===this.dropbarMode&&le(this.dropbar,"uk-navbar-dropbar-slide"))},disconnected:function(){this.dropbar&&te(this.dropbar)},update:function(){var e=this;this.$create("drop",this.dropdowns.filter(function(t){return!e.getDropdown(t)}),Y({},this.$props,{boundary:this.boundary,pos:this.pos,offset:this.dropbar||this.offset}))},events:[{name:"mouseover",delegate:function(){return this.dropdown},handler:function(t){var e=t.current,i=this.getActive();i&&i.toggle&&!St(i.toggle.$el,e)&&!i.tracker.movesTo(i.$el)&&i.hide(!1)}},{name:"mouseleave",el:function(){return this.dropbar},handler:function(){var t=this.getActive();t&&!ft(this.dropbar,":hover")&&t.hide()}},{name:"beforeshow",capture:!0,filter:function(){return this.dropbar},handler:function(){this.dropbar.parentNode||Zt(this.dropbarAnchor||this.$el,this.dropbar)}},{name:"show",capture:!0,filter:function(){return this.dropbar},handler:function(t,e){var i=e.$el,n=e.dir;this.clsDrop&&le(i,this.clsDrop+"-dropbar"),"bottom"===n&&this.transitionTo(i.offsetHeight+P(be(i,"marginTop"))+P(be(i,"marginBottom")),i)}},{name:"beforehide",filter:function(){return this.dropbar},handler:function(t,e){var i=e.$el,n=this.getActive();ft(this.dropbar,":hover")&&n&&n.$el===i&&t.preventDefault()}},{name:"hide",filter:function(){return this.dropbar},handler:function(t,e){var i=e.$el,n=this.getActive();(!n||n&&n.$el===i)&&this.transitionTo(0)}}],methods:{getActive:function(){var t=this.dropdowns.map(this.getDropdown).filter(function(t){return t.isActive()})[0];return t&&b(t.mode,"hover")&&St(t.toggle.$el,this.$el)&&t},transitionTo:function(t,e){var i=this.dropbar,n=xt(i)?Ve(i):0;return be(e=n<t&&e,"clip","rect(0,"+e.offsetWidth+"px,"+n+"px,0)"),Ve(i,n),_e.cancel([e,i]),zt.all([_e.start(i,{height:t},this.duration),_e.start(e,{clip:"rect(0,"+e.offsetWidth+"px,"+t+"px,0)"},this.duration)]).catch(X).then(function(){return be(e,{clip:""})})},getDropdown:function(t){return this.$getComponent(t,"drop")||this.$getComponent(t,"dropdown")}}},Yn={mixins:[Wn],args:"mode",props:{content:String,mode:String,flip:Boolean,overlay:Boolean},data:{content:".uk-offcanvas-content",mode:"slide",flip:!1,overlay:!1,clsPage:"uk-offcanvas-page",clsContainer:"uk-offcanvas-container",selPanel:".uk-offcanvas-bar",clsFlip:"uk-offcanvas-flip",clsContent:"uk-offcanvas-content",clsContentAnimation:"uk-offcanvas-content-animation",clsSidebarAnimation:"uk-offcanvas-bar-animation",clsMode:"uk-offcanvas",clsOverlay:"uk-offcanvas-overlay",selClose:".uk-offcanvas-close"},computed:{content:function(t){return Be(t.content)||document.body},clsFlip:function(t){var e=t.flip,i=t.clsFlip;return e?i:""},clsOverlay:function(t){var e=t.overlay,i=t.clsOverlay;return e?i:""},clsMode:function(t){var e=t.mode;return t.clsMode+"-"+e},clsSidebarAnimation:function(t){var e=t.mode,i=t.clsSidebarAnimation;return"none"===e||"reveal"===e?"":i},clsContentAnimation:function(t){var e=t.mode,i=t.clsContentAnimation;return"push"!==e&&"reveal"!==e?"":i},transitionElement:function(t){return"reveal"===t.mode?this.panel.parentNode:this.panel}},update:{write:function(){this.getActive()===this&&((this.overlay||this.clsContentAnimation)&&Ye(this.content,Ye(window)-this.scrollbarWidth),this.overlay&&(Ve(this.content,Ve(window)),jn&&(this.content.scrollTop=jn.y)))},events:["resize"]},events:[{name:"click",delegate:function(){return'a[href^="#"]'},handler:function(t){var e=t.current;e.hash&&Be(e.hash,this.content)&&(jn=null,this.hide())}},{name:"beforescroll",filter:function(){return this.overlay},handler:function(t,e,i){e&&i&&this.isToggled()&&Be(i,this.content)&&(Ct(this.$el,"hidden",function(){return e.scrollTo(i)}),t.preventDefault())}},{name:"show",self:!0,handler:function(){jn=jn||{x:window.pageXOffset,y:window.pageYOffset},"reveal"!==this.mode||de(this.panel,this.clsMode)||(ee(this.panel,"<div>"),le(this.panel.parentNode,this.clsMode)),be(document.documentElement,"overflowY",(!this.clsContentAnimation||this.flip)&&this.scrollbarWidth&&this.overlay?"scroll":""),le(document.body,this.clsContainer,this.clsFlip,this.clsOverlay),Ve(document.body),le(this.content,this.clsContentAnimation),le(this.panel,this.clsSidebarAnimation,"reveal"!==this.mode?this.clsMode:""),le(this.$el,this.clsOverlay),be(this.$el,"display","block"),Ve(this.$el)}},{name:"hide",self:!0,handler:function(){he(this.content,this.clsContentAnimation);var t=this.getActive();("none"===this.mode||t&&t!==this&&t!==this.prev)&&_t(this.panel,"transitionend")}},{name:"hidden",self:!0,handler:function(){if("reveal"===this.mode&&ne(this.panel),this.overlay){if(!jn){var t=this.content,e=t.scrollLeft,i=t.scrollTop;jn={x:e,y:i}}}else jn={x:window.pageXOffset,y:window.pageYOffset};he(this.panel,this.clsSidebarAnimation,this.clsMode),he(this.$el,this.clsOverlay),be(this.$el,"display",""),he(document.body,this.clsContainer,this.clsFlip,this.clsOverlay),document.body.scrollTop=jn.y,be(document.documentElement,"overflowY",""),Ye(this.content,""),Ve(this.content,""),window.scroll(jn.x,jn.y),jn=null}},{name:"swipeLeft swipeRight",handler:function(t){this.isToggled()&&Di(t)&&("swipeLeft"===t.type&&!this.flip||"swipeRight"===t.type&&this.flip)&&this.hide()}}]},Rn={mixins:[Vi],props:{selModal:String,selPanel:String},data:{selModal:".uk-modal",selPanel:".uk-modal-dialog"},computed:{modal:function(t,e){return mt(e,t.selModal)},panel:function(t,e){return mt(e,t.selPanel)}},connected:function(){be(this.$el,"minHeight",150)},update:{read:function(){return!(!this.panel||!this.modal)&&{current:P(be(this.$el,"maxHeight")),max:Math.max(150,Ve(this.modal)-(Le(this.panel).height-Ve(this.$el)))}},write:function(t){var e=t.current,i=t.max;be(this.$el,"maxHeight",i),Math.round(e)!==Math.round(i)&&_t(this.$el,"resize")},events:["load","resize"]}},qn={props:["width","height"],connected:function(){le(this.$el,"uk-responsive-width")},update:{read:function(){return!!(xt(this.$el)&&this.width&&this.height)&&{width:Ye(this.$el.parentNode),height:this.height}},write:function(t){Ve(this.$el,G.contain({height:this.height,width:this.width},t).height)},events:["load","resize"]}},Un={props:{duration:Number,offset:Number},data:{duration:1e3,offset:0},methods:{scrollTo:function(i){var n=this;i=i&&Be(i)||document.body;var t=Ve(document),e=Ve(window),r=Le(i).top-this.offset;if(t<r+e&&(r=t-e),_t(this.$el,"beforescroll",[this,i])){var o=Date.now(),s=window.pageYOffset,a=function(){var t,e=s+(r-s)*(t=U((Date.now()-o)/n.duration),.5*(1-Math.cos(Math.PI*t)));Qe(window,e),e!==r?requestAnimationFrame(a):_t(n.$el,"scrolled",[n,i])};a()}}},events:{click:function(t){t.defaultPrevented||(t.preventDefault(),this.scrollTo(wt(this.$el.hash).substr(1)))}}};var Xn={args:"cls",props:{cls:"list",target:String,hidden:Boolean,offsetTop:Number,offsetLeft:Number,repeat:Boolean,delay:Number},data:function(){return{cls:[],target:!1,hidden:!0,offsetTop:0,offsetLeft:0,repeat:!1,delay:0,inViewClass:"uk-scrollspy-inview"}},computed:{elements:function(t,e){var i=t.target;return i?ze(i,e):[e]}},update:[{write:function(){this.hidden&&be(It(this.elements,":not(."+this.inViewClass+")"),"visibility","hidden")}},{read:function(r){var o=this;r.delay&&this.elements.forEach(function(t,e){var i=r[e];if(!i||i.el!==t){var n=it(t,"uk-scrollspy-class");i={el:t,toggles:n&&n.split(",")||o.cls}}i.show=Ge(t,o.offsetTop,o.offsetLeft),r[e]=i})},write:function(o){var s=this;if(!o.delay)return this.$emit(),o.delay=!0;var a=1===this.elements.length?1:0;this.elements.forEach(function(t,e){var i=o[e],n=i.toggles[e]||i.toggles[0];if(!i.show||i.inview||i.timer)!i.show&&i.inview&&s.repeat&&(i.timer&&(clearTimeout(i.timer),delete i.timer),be(t,"visibility",s.hidden?"hidden":""),he(t,s.inViewClass),fe(t,n),_t(t,"outview"),s.$update(t),i.inview=!1);else{var r=function(){be(t,"visibility",""),le(t,s.inViewClass),fe(t,n),_t(t,"inview"),s.$update(t),i.inview=!0,delete i.timer};s.delay&&a?i.timer=setTimeout(r,s.delay*a):r(),a++}})},events:["scroll","load","resize"]}]},Jn={props:{cls:String,closest:String,scroll:Boolean,overflow:Boolean,offset:Number},data:{cls:"uk-active",closest:!1,scroll:!1,overflow:!0,offset:0},computed:{links:function(t,e){return ze('a[href^="#"]',e).filter(function(t){return t.hash})},elements:function(){return this.closest?mt(this.links,this.closest):this.links},targets:function(){return ze(this.links.map(function(t){return t.hash}).join(","))}},update:[{read:function(){this.scroll&&this.$create("scroll",this.links,{offset:this.offset||0})}},{read:function(o){var s=this,a=window.pageYOffset+this.offset+1,l=Ve(document)-Ve(window)+this.offset;o.active=!1,this.targets.every(function(t,e){var i=Le(t).top,n=e+1===s.targets.length;if(!s.overflow&&(0===e&&a<i||n&&i+t.offsetTop<a))return!1;if(!n&&Le(s.targets[e+1]).top<=a)return!0;if(l<=a)for(var r=s.targets.length-1;e<r;r--)if(Ge(s.targets[r])){t=s.targets[r];break}return!(o.active=Be(It(s.links,'[href="#'+t.id+'"]')))})},write:function(t){var e=t.active;this.links.forEach(function(t){return t.blur()}),he(this.elements,this.cls),e&&_t(this.$el,"active",[e,le(this.closest?mt(e,this.closest):e,this.cls)])},events:["scroll","load","resize"]}]},Kn={mixins:[Vi],attrs:!0,props:{top:null,bottom:Boolean,offset:Number,animation:String,clsActive:String,clsInactive:String,clsFixed:String,clsBelow:String,selTarget:String,widthElement:Boolean,showOnUp:Boolean,media:"media",targetOffset:Number},data:{top:0,bottom:!1,offset:0,animation:"",clsActive:"uk-active",clsInactive:"",clsFixed:"uk-sticky-fixed",clsBelow:"uk-sticky-below",selTarget:"",widthElement:!1,showOnUp:!1,media:!1,targetOffset:!1},computed:{selTarget:function(t,e){var i=t.selTarget;return i&&Be(i,e)||e},widthElement:function(t,e){return nt(t.widthElement,e)||this.placeholder}},connected:function(){this.placeholder=Be("+ .uk-sticky-placeholder",this.$el)||Be('<div class="uk-sticky-placeholder"></div>'),this.isActive||this.hide()},disconnected:function(){this.isActive&&(this.isActive=!1,this.hide(),he(this.selTarget,this.clsInactive)),te(this.placeholder),this.placeholder=null,this.widthElement=null},events:[{name:"active",self:!0,handler:function(){ue(this.selTarget,this.clsInactive,this.clsActive)}},{name:"inactive",self:!0,handler:function(){ue(this.selTarget,this.clsActive,this.clsInactive)}},{name:"load hashchange popstate",el:window,handler:function(){var n=this;if(!1!==this.targetOffset&&location.hash&&0<window.pageYOffset){var r=Be(location.hash);r&&fi.read(function(){var t=Le(r).top,e=Le(n.$el).top,i=n.$el.offsetHeight;n.isActive&&t<=e+i&&e<=t+r.offsetHeight&&Qe(window,t-i-(M(n.targetOffset)?n.targetOffset:0)-n.offset)})}}}],update:[{read:function(){return{height:this.$el.offsetHeight,top:Le(this.isActive?this.placeholder:this.$el).top}},write:function(t){var e=t.height,i=t.top,n=this.placeholder;be(n,Y({height:"absolute"!==be(this.$el,"position")?e:""},be(this.$el,["marginTop","marginBottom","marginLeft","marginRight"]))),St(n,document)||(Zt(this.$el,n),Z(n,"hidden","")),this.topOffset=i,this.bottomOffset=this.topOffset+e;var r=Gn("bottom",this);this.top=Math.max(P(Gn("top",this)),this.topOffset)-this.offset,this.bottom=r&&r-e,this.inactive=this.media&&!window.matchMedia(this.media).matches},events:["load","resize"]},{read:function(t,e){var i=e.scrollY;return void 0===i&&(i=window.pageYOffset),this.width=(xt(this.widthElement)?this.widthElement:this.$el).offsetWidth,{scroll:this.scroll=i,visible:xt(this.$el)}},write:function(t,e){var i=this,n=t.visible,r=t.scroll;void 0===e&&(e={});var o=e.dir;if(!(r<0||!n||this.disabled||this.showOnUp&&!o))if(this.inactive||r<this.top||this.showOnUp&&(r<=this.top||"down"===o||"up"===o&&!this.isActive&&r<=this.bottomOffset)){if(!this.isActive)return;this.isActive=!1,this.animation&&r>this.topOffset?(De.cancel(this.$el),De.out(this.$el,this.animation).then(function(){return i.hide()},X)):this.hide()}else this.isActive?this.update():this.animation?(De.cancel(this.$el),this.show(),De.in(this.$el,this.animation).catch(X)):this.show()},events:["load","resize","scroll"]}],methods:{show:function(){this.isActive=!0,this.update(),Z(this.placeholder,"hidden",null)},hide:function(){this.isActive&&!de(this.selTarget,this.clsActive)||_t(this.$el,"inactive"),he(this.$el,this.clsFixed,this.clsBelow),be(this.$el,{position:"",top:"",width:""}),Z(this.placeholder,"hidden","")},update:function(){var t=0!==this.top||this.scroll>this.top,e=Math.max(0,this.offset);this.bottom&&this.scroll>this.bottom-this.offset&&(e=this.bottom-this.scroll),be(this.$el,{position:"fixed",top:e+"px",width:this.width}),de(this.selTarget,this.clsActive)?t||_t(this.$el,"inactive"):t&&_t(this.$el,"active"),fe(this.$el,this.clsBelow,this.scroll>this.bottomOffset),le(this.$el,this.clsFixed)}}};function Gn(t,e){var i=e.$props,n=e.$el,r=e[t+"Offset"],o=i[t];if(o){if(M(o))return r+P(o);if(N(o)&&o.match(/^-?\d+vh$/))return Ve(window)*P(o)/100;var s=!0===o?n.parentNode:nt(o,n);return s?Le(s).top+s.offsetHeight:void 0}}var Zn,Qn={mixins:[Yi],args:"connect",props:{connect:String,toggle:String,active:Number,swiping:Boolean},data:{connect:"~.uk-switcher",toggle:"> *",active:0,swiping:!0,cls:"uk-active",clsContainer:"uk-switcher",attrItem:"uk-switcher-item",queued:!0},computed:{connects:function(t,e){return rt(t.connect,e)},toggles:function(t,e){return ze(t.toggle,e)}},events:[{name:"click",delegate:function(){return this.toggle+":not(.uk-disabled)"},handler:function(t){t.preventDefault(),this.show(t.current)}},{name:"click",el:function(){return this.connects},delegate:function(){return"["+this.attrItem+"],[data-"+this.attrItem+"]"},handler:function(t){t.preventDefault(),this.show(it(t.current,this.attrItem))}},{name:"swipeRight swipeLeft",filter:function(){return this.swiping},el:function(){return this.connects},handler:function(t){Di(t)&&(t.preventDefault(),window.getSelection().toString()||this.show("swipeLeft"===t.type?"next":"previous"))}}],update:function(){var e=this;this.connects.forEach(function(t){return e.updateAria(t.children)}),this.show(It(this.toggles,"."+this.cls)[0]||this.toggles[this.active]||this.toggles[0])},methods:{index:function(){return!!this.connects.length&&qt(It(this.connects[0].children,"."+this.cls)[0])},show:function(t){for(var e,i=this,n=this.toggles.length,r=this.index(),o=0<=r,s="previous"===t?-1:1,a=Ut(t,this.toggles,r),l=0;l<n;l++,a=(a+s+n)%n)if(!ft(i.toggles[a],".uk-disabled, [disabled]")){e=i.toggles[a];break}!e||0<=r&&de(e,this.cls)||r===a||(he(this.toggles,this.cls),Z(this.toggles,"aria-expanded",!1),le(e,this.cls),Z(e,"aria-expanded",!0),this.connects.forEach(function(t){o?i.toggleElement([t.children[r],t.children[a]]):i.toggleNow(t.children[a])}))}}},tr={mixins:[Vi],extends:Qn,props:{media:"media"},data:{media:960,attrItem:"uk-tab-item"},connected:function(){var t=de(this.$el,"uk-tab-left")?"uk-tab-left":!!de(this.$el,"uk-tab-right")&&"uk-tab-right";t&&this.$create("toggle",this.$el,{cls:t,mode:"media",media:this.media})}},er={mixins:[Yi],args:"target",props:{href:String,target:null,mode:"list",media:"media"},data:{href:!1,target:!1,mode:"click",queued:!0,media:!1},computed:{target:function(t,e){var i=t.href,n=t.target;return(n=rt(n||i,e)).length&&n||[e]}},events:[{name:ui+" "+di,filter:function(){return b(this.mode,"hover")},handler:function(t){Di(t)||this.toggle("toggle"+(t.type===ui?"show":"hide"))}},{name:"click",filter:function(){return b(this.mode,"click")||ai&&b(this.mode,"hover")},handler:function(t){var e;(Di(t)||b(this.mode,"click"))&&((mt(t.target,'a[href="#"], button')||(e=mt(t.target,"a[href]"))&&(this.cls||!xt(this.target)||e.hash&&ft(this.target,e.hash)))&&Ct(document,"click",function(t){return t.preventDefault()}),this.toggle())}}],update:{write:function(){if(b(this.mode,"media")&&this.media){var t=this.isToggled(this.target);(window.matchMedia(this.media).matches?!t:t)&&this.toggle()}},events:["load","resize"]},methods:{toggle:function(t){_t(this.target,t||"toggle",[this])&&this.toggleElement(this.target)}}};Fi.version="3.0.0-rc.10",(Zn=Fi).component("accordion",Ri),Zn.component("alert",qi),Zn.component("cover",Gi),Zn.component("drop",Qi),Zn.component("dropdown",tn),Zn.component("formCustom",en),Zn.component("gif",nn),Zn.component("grid",an),Zn.component("heightMatch",ln),Zn.component("heightViewport",hn),Zn.component("icon",vn),Zn.component("img",$n),Zn.component("leader",Pn),Zn.component("margin",rn),Zn.component("modal",Ln),Zn.component("nav",Fn),Zn.component("navbar",Vn),Zn.component("offcanvas",Yn),Zn.component("overflowAuto",Rn),Zn.component("responsive",qn),Zn.component("scroll",Un),Zn.component("scrollspy",Xn),Zn.component("scrollspyNav",Jn),Zn.component("sticky",Kn),Zn.component("svg",dn),Zn.component("switcher",Qn),Zn.component("tab",tr),Zn.component("toggle",er),Zn.component("video",Ki),Zn.component("close",xn),Zn.component("marker",wn),Zn.component("navbarToggleIcon",wn),Zn.component("overlayIcon",wn),Zn.component("paginationNext",wn),Zn.component("paginationPrevious",wn),Zn.component("searchIcon",yn),Zn.component("slidenavNext",bn),Zn.component("slidenavPrevious",bn),Zn.component("spinner",kn),Zn.component("totop",wn),Zn.use(Ui);var ir={mixins:[Vi],attrs:!0,props:{date:String,clsWrapper:String},data:{date:"",clsWrapper:".uk-countdown-%unit%"},computed:{date:function(t){var e=t.date;return Date.parse(e)},days:function(t,e){return Be(t.clsWrapper.replace("%unit%","days"),e)},hours:function(t,e){return Be(t.clsWrapper.replace("%unit%","hours"),e)},minutes:function(t,e){return Be(t.clsWrapper.replace("%unit%","minutes"),e)},seconds:function(t,e){return Be(t.clsWrapper.replace("%unit%","seconds"),e)},units:function(){var e=this;return["days","hours","minutes","seconds"].filter(function(t){return e[t]})}},connected:function(){this.start()},disconnected:function(){var e=this;this.stop(),this.units.forEach(function(t){return Xt(e[t])})},events:[{name:"visibilitychange",el:document,handler:function(){document.hidden?this.stop():this.start()}}],update:{write:function(){var t,e,n=this,r=(t=this.date,{total:e=t-Date.now(),seconds:e/1e3%60,minutes:e/1e3/60%60,hours:e/1e3/60/60%24,days:e/1e3/60/60/24});r.total<=0&&(this.stop(),r.days=r.hours=r.minutes=r.seconds=0),this.units.forEach(function(t){var e=String(Math.floor(r[t]));e=e.length<2?"0"+e:e;var i=n[t];i.textContent!==e&&((e=e.split("")).length!==i.children.length&&Jt(i,e.map(function(){return"<span></span>"}).join("")),e.forEach(function(t,e){return i.children[e].textContent=t}))})}},methods:{start:function(){var t=this;this.stop(),this.date&&this.units.length&&(this.$emit(),this.timer=setInterval(function(){return t.$emit()},1e3))},stop:function(){this.timer&&(clearInterval(this.timer),this.timer=null)}}};var nr,rr="uk-animation-target",or={props:{animation:Number},data:{animation:150},computed:{target:function(){return this.$el}},methods:{animate:function(t){var n=this;nr||(nr=Kt(document.head,"<style>").sheet).insertRule("."+rr+" > * {\n                    margin-top: 0 !important;\n                    transform: none !important;\n                }",0);var r=L(this.target.children),o=r.map(function(t){return sr(t,!0)}),e=Ve(this.target),i=window.pageYOffset;t(),_e.cancel(this.target),r.forEach(_e.cancel),ar(this.target),this.$update(this.target),fi.flush();var s=Ve(this.target),a=(r=r.concat(L(this.target.children).filter(function(t){return!b(r,t)}))).map(function(t,e){return!!(t.parentNode&&e in o)&&(o[e]?xt(t)?lr(t):{opacity:0}:{opacity:xt(t)?1:0})});return o=a.map(function(t,e){var i=r[e].parentNode===n.target&&(o[e]||sr(r[e]));if(i)if(t){if(!("opacity"in t)){i.opacity%1?t.opacity=1:delete i.opacity}}else delete i.opacity;return i}),le(this.target,rr),r.forEach(function(t,e){return o[e]&&be(t,o[e])}),be(this.target,"height",e),Qe(window,i),zt.all(r.map(function(t,e){return o[e]&&a[e]?_e.start(t,a[e],n.animation,"ease"):zt.resolve()}).concat(_e.start(this.target,{height:s},this.animation,"ease"))).then(function(){r.forEach(function(t,e){return be(t,{display:0===a[e].opacity?"none":"",zIndex:""})}),ar(n.target),n.$update(n.target),fi.flush()},X)}}};function sr(t,e){var i=be(t,"zIndex");return!!xt(t)&&Y({display:"",opacity:e?be(t,"opacity"):"0",pointerEvents:"none",position:"absolute",zIndex:"auto"===i?qt(t):i},lr(t))}function ar(t){be(t.children,{height:"",left:"",opacity:"",pointerEvents:"",position:"",top:"",width:""}),he(t,rr),be(t,"height","")}function lr(t){var e=t.getBoundingClientRect(),i=e.height,n=e.width,r=Fe(t),o=r.top,s=r.left;return{top:o+=P(be(t,"marginTop")),left:s,height:i,width:n}}var hr={mixins:[or],args:"target",attrs:!0,props:{target:Boolean,selActive:Boolean},data:{target:null,selActive:!1,attrItem:"uk-filter-control",cls:"uk-active",animation:250},computed:{toggles:function(t,e){t.attrItem;return ze("["+this.attrItem+"],[data-"+this.attrItem+"]",e)},target:function(t,e){return Be(t.target,e)}},events:[{name:"click",delegate:function(){return"["+this.attrItem+"],[data-"+this.attrItem+"]"},handler:function(t){t.preventDefault(),this.apply(t.current)}}],connected:function(){var e=this;if(!1!==this.selActive){var i=ze(this.selActive,this.$el);this.toggles.forEach(function(t){return fe(t,e.cls,b(i,t))})}},update:function(t){var e=t.toggles,i=t.children;dr(e,this.toggles,!1)&&dr(i,this.target.children,!1)||(t.toggles=this.toggles,t.children=this.target.children,this.setState(this.getState(),!1))},methods:{apply:function(t){this.setState(ur(t,this.attrItem,this.getState()))},getState:function(){var i=this;return this.toggles.filter(function(t){return de(t,i.cls)}).reduce(function(t,e){return ur(e,i.attrItem,t)},{filter:{"":""},sort:[]})},setState:function(h,t){var c=this;void 0===t&&(t=!0),h=Y({filter:{"":""},sort:[]},h),_t(this.$el,"beforeFilter",[this,h]);var u=L(this.target.children);this.toggles.forEach(function(t){return fe(t,c.cls,function(t,e,i){var n=i.filter,r=i.sort,o=r[0],s=r[1],a=cr(t,e),l=a.filter,h=a.group;void 0===h&&(h="");var c=a.sort,u=a.order;void 0===u&&(u="asc");return Boolean((l||D(c))&&h in n&&(l===n[h]||D(l)&&!n[h])||o&&c&&o===c&&s===u)}(t,c.attrItem,h))});var e=function(){var t,e,i=(t=h.filter,e="",R(t,function(t){return e+=t||""}),e);u.forEach(function(t){return be(t,"display",i&&!ft(t,i)?"none":"")});var n,r,o=h.sort,s=o[0],a=o[1];if(s){var l=(n=s,r=a,L(u).sort(function(t,e){return it(t,n).localeCompare(it(e,n),void 0,{numeric:!0})*("asc"===r||-1)}));dr(l,u)||l.forEach(function(t){return Kt(c.target,t)})}};t?this.animate(e).then(function(){return _t(c.$el,"afterFilter",[c])}):(e(),_t(this.$el,"afterFilter",[this]))}}};function cr(t,e){return $i(it(t,e),["filter"])}function ur(t,s,a){return L(t).forEach(function(t){var e=cr(t,s),i=e.filter,n=e.group,r=e.sort,o=e.order;void 0===o&&(o="asc"),(i||D(r))&&(n?(delete a.filter[""],a.filter[n]=i):a.filter={"":i}),D(r)||(a.sort=[r,o])}),a}function dr(t,i,n){return void 0===n&&(n=!0),t=L(t),i=L(i),t.length===i.length&&t.every(function(t,e){return n?t===i[e]:~i.indexOf(t)})}var fr={slide:{show:function(t){return[{transform:mr(-100*t)},{transform:mr()}]},percent:function(t){return pr(t)},translate:function(t,e){return[{transform:mr(-100*e*t)},{transform:mr(100*e*(1-t))}]}}};function pr(t){return Math.abs(be(t,"transform").split(",")[4]/t.offsetWidth)||0}function mr(t,e){return void 0===t&&(t=0),void 0===e&&(e="%"),"translateX("+t+(t?e:"")+")"}function gr(t){return"scale3d("+t+", "+t+", 1)"}var vr=Y({},fr,{fade:{show:function(){return[{opacity:0},{opacity:1}]},percent:function(t){return 1-be(t,"opacity")},translate:function(t){return[{opacity:1-t},{opacity:t}]}},scale:{show:function(){return[{opacity:0,transform:gr(.8)},{opacity:1,transform:gr(1)}]},percent:function(t){return 1-be(t,"opacity")},translate:function(t){return[{opacity:1-t,transform:gr(1-.2*t)},{opacity:t,transform:gr(.8+.2*t)}]}}});function wr(t,e,i){_t(t,At(e,!1,!1,i))}var br={mixins:[{props:{autoplay:Boolean,autoplayInterval:Number,pauseOnHover:Boolean},data:{autoplay:!1,autoplayInterval:7e3,pauseOnHover:!0},connected:function(){this.startAutoplay()},disconnected:function(){this.stopAutoplay()},events:[{name:"visibilitychange",el:document,handler:function(){document.hidden?this.stopAutoplay():this.startAutoplay()}},{name:li,handler:"stopAutoplay"},{name:"mouseenter",filter:function(){return this.autoplay},handler:function(){this.isHovering=!0}},{name:"mouseleave",filter:function(){return this.autoplay},handler:function(){this.isHovering=!1}}],methods:{startAutoplay:function(){var t=this;this.stopAutoplay(),this.autoplay&&(this.interval=setInterval(function(){return!(t.isHovering&&t.pauseOnHover)&&!t.stack.length&&t.show("next")},this.autoplayInterval))},stopAutoplay:function(){this.interval&&clearInterval(this.interval)}}},{data:{threshold:10,preventCatch:!1},init:function(){var n=this;["start","move","end"].forEach(function(t){var i=n[t];n[t]=function(t){var e=Bi(t).x*(ri?-1:1);n.prevPos=e!==n.pos?n.pos:n.prevPos,n.pos=e,i(t)}})},events:[{name:li,delegate:function(){return this.slidesSelector},handler:function(t){var e;!Di(t)&&(!(e=t.target).children.length&&e.childNodes.length)||0<t.button||this.length<2||this.preventCatch||this.start(t)}},{name:"touchmove",passive:!1,handler:"move",delegate:function(){return this.slidesSelector}},{name:"dragstart",handler:function(t){t.preventDefault()}}],methods:{start:function(){var t=this;this.drag=this.pos,this._transitioner?(this.percent=this._transitioner.percent(),this.drag+=this._transitioner.getDistance()*this.percent*this.dir,this._transitioner.cancel(),this._transitioner.translate(this.percent),this.dragging=!0,this.stack=[]):this.prevIndex=this.index;var e=Tt(document,hi.replace(" touchmove",""),this.move,{passive:!1});this.unbindMove=function(){e(),t.unbindMove=null},Tt(window,"scroll",this.unbindMove),Tt(document,ci,this.end,!0)},move:function(t){var e=this;if(this.unbindMove){var i=this.pos-this.drag;if(!(0===i||this.prevPos===this.pos||!this.dragging&&Math.abs(i)<this.threshold)){t.cancelable&&t.preventDefault(),this.dragging=!0,this.dir=i<0?1:-1;for(var n=this.slides,r=this.prevIndex,o=Math.abs(i),s=this.getIndex(r+this.dir,r),a=this._getDistance(r,s)||n[r].offsetWidth;s!==r&&a<o;)e.drag-=a*e.dir,r=s,o-=a,s=e.getIndex(r+e.dir,r),a=e._getDistance(r,s)||n[r].offsetWidth;this.percent=o/a;var l,h=n[r],c=n[s],u=this.index!==s,d=r===s;[this.index,this.prevIndex].filter(function(t){return!b([s,r],t)}).forEach(function(t){_t(n[t],"itemhidden",[e]),d&&(l=!0,e.prevIndex=r)}),(this.index===r&&this.prevIndex!==r||l)&&_t(n[this.index],"itemshown",[this]),u&&(this.prevIndex=r,this.index=s,!d&&_t(h,"beforeitemhide",[this]),_t(c,"beforeitemshow",[this])),this._transitioner=this._translate(Math.abs(this.percent),h,!d&&c),u&&(!d&&_t(h,"itemhide",[this]),_t(c,"itemshow",[this]))}}},end:function(){if(Et(window,"scroll",this.unbindMove),this.unbindMove&&this.unbindMove(),Et(document,ci,this.end,!0),this.dragging){if(this.dragging=null,this.index===this.prevIndex)this.percent=1-this.percent,this.dir*=-1,this._show(!1,this.index,!0),this._transitioner=null;else{var t=(ri?this.dir*(ri?1:-1):this.dir)<0==this.prevPos>this.pos;this.index=t?this.index:this.prevIndex,t&&(this.percent=1-this.percent),this.show(0<this.dir&&!t||this.dir<0&&t?"next":"previous",!0)}Bt()}this.drag=this.percent=null}}},{data:{selNav:!1},computed:{nav:function(t,e){return Be(t.selNav,e)},navItemSelector:function(t){var e=t.attrItem;return"["+e+"],[data-"+e+"]"},navItems:function(t,e){return ze(this.navItemSelector,e)}},update:[{write:function(){var i=this;this.nav&&this.length!==this.nav.children.length&&Jt(this.nav,this.slides.map(function(t,e){return"<li "+i.attrItem+'="'+e+'"><a href="#"></a></li>'}).join("")),fe(ze(this.navItemSelector,this.$el).concat(this.nav),"uk-hidden",!this.maxIndex),this.updateNav()},events:["load","resize"]}],events:[{name:"click",delegate:function(){return this.navItemSelector},handler:function(t){t.preventDefault(),t.current.blur(),this.show(it(t.current,this.attrItem))}},{name:"itemshow",handler:"updateNav"}],methods:{updateNav:function(){var i=this,n=this.getValidIndex();this.navItems.forEach(function(t){var e=it(t,i.attrItem);fe(t,i.clsActive,z(e)===n),fe(t,"uk-invisible",i.finite&&("previous"===e&&0===n||"next"===e&&n>=i.maxIndex))})}}}],attrs:!0,props:{clsActivated:Boolean,easing:String,index:Number,finite:Boolean,velocity:Number},data:function(){return{easing:"ease",finite:!1,velocity:1,index:0,stack:[],percent:0,clsActive:"uk-active",clsActivated:!1,Transitioner:!1,transitionOptions:{}}},computed:{duration:function(t,e){var i=t.velocity;return yr(e.offsetWidth/i)},length:function(){return this.slides.length},list:function(t,e){return Be(t.selList,e)},maxIndex:function(){return this.length-1},slidesSelector:function(t){return t.selList+" > *"},slides:function(){return L(this.list.children)}},events:{itemshown:function(){this.$update(this.list)}},methods:{show:function(t,e){var i=this;if(void 0===e&&(e=!1),!this.dragging&&this.length){var n=this.stack,r=e?0:n.length,o=function(){n.splice(r,1),n.length&&i.show(n.shift(),!0)};if(n[e?"unshift":"push"](t),!e&&1<n.length)2===n.length&&this._transitioner.forward(Math.min(this.duration,200));else{var s=this.index,a=de(this.slides,this.clsActive)&&this.slides[s],l=this.getIndex(t,this.index),h=this.slides[l];if(a!==h){var c,u;if(this.dir=(u=s,"next"===(c=t)?1:"previous"===c?-1:c<u?-1:1),this.prevIndex=s,this.index=l,a&&_t(a,"beforeitemhide",[this]),!_t(h,"beforeitemshow",[this,a]))return this.index=this.prevIndex,void o();var d=this._show(a,h,e).then(function(){return a&&_t(a,"itemhidden",[i]),_t(h,"itemshown",[i]),new zt(function(t){fi.write(function(){n.shift(),n.length?i.show(n.shift(),!0):i._transitioner=null,t()})})});return a&&_t(a,"itemhide",[this]),_t(h,"itemshow",[this]),d}o()}}},getIndex:function(t,e){return void 0===t&&(t=this.index),void 0===e&&(e=this.index),U(Ut(t,this.slides,e,this.finite),0,this.maxIndex)},getValidIndex:function(t,e){return void 0===t&&(t=this.index),void 0===e&&(e=this.prevIndex),this.getIndex(t,e)},_show:function(t,e,i){if(this._transitioner=this._getTransitioner(t,e,this.dir,Y({easing:i?e.offsetWidth<600?"cubic-bezier(0.25, 0.46, 0.45, 0.94)":"cubic-bezier(0.165, 0.84, 0.44, 1)":this.easing},this.transitionOptions)),!i&&!t)return this._transitioner.translate(1),zt.resolve();var n=this.stack.length;return this._transitioner[1<n?"forward":"show"](1<n?Math.min(this.duration,75+75/(n-1)):this.duration,this.percent)},_getDistance:function(t,e){return new this._getTransitioner(t,t!==e&&e).getDistance()},_translate:function(t,e,i){void 0===e&&(e=this.prevIndex),void 0===i&&(i=this.index);var n=this._getTransitioner(e!==i&&e,i);return n.translate(t),n},_getTransitioner:function(t,e,i,n){return void 0===t&&(t=this.prevIndex),void 0===e&&(e=this.index),void 0===i&&(i=this.dir||1),void 0===n&&(n=this.transitionOptions),new this.Transitioner(O(t)?this.slides[t]:t,O(e)?this.slides[e]:e,i*(ri?-1:1),n)}}};function yr(t){return.5*t+300}var xr={mixins:[br],props:{animation:String},data:{animation:"slide",clsActivated:"uk-transition-active",Animations:fr,Transitioner:function(o,s,a,t){var e=t.animation,l=t.easing,i=e.percent,n=e.translate,r=e.show;void 0===r&&(r=X);var h=r(a),c=new Pt;return{dir:a,show:function(t,e,i){var n=this;void 0===e&&(e=0);var r=i?"linear":l;return t-=Math.round(t*U(e,-1,1)),this.translate(e),wr(s,"itemin",{percent:e,duration:t,timing:r,dir:a}),wr(o,"itemout",{percent:1-e,duration:t,timing:r,dir:a}),zt.all([_e.start(s,h[1],t,r),_e.start(o,h[0],t,r)]).then(function(){n.reset(),c.resolve()},X),c.promise},stop:function(){return _e.stop([s,o])},cancel:function(){_e.cancel([s,o])},reset:function(){for(var t in h[0])be([s,o],t,"")},forward:function(t,e){return void 0===e&&(e=this.percent()),_e.cancel([s,o]),this.show(t,e,!0)},translate:function(t){this.reset();var e=n(t,a);be(s,e[1]),be(o,e[0]),wr(s,"itemtranslatein",{percent:t,dir:a}),wr(o,"itemtranslateout",{percent:1-t,dir:a})},percent:function(){return i(o||s,s,a)},getDistance:function(){return o.offsetWidth}}}},computed:{animation:function(t){var e=t.animation,i=t.Animations;return Y(e in i?i[e]:i.slide,{name:e})},transitionOptions:function(){return{animation:this.animation}}},events:{"itemshow itemhide itemshown itemhidden":function(t){var e=t.target;this.$update(e)},itemshow:function(){O(this.prevIndex)&&fi.flush()},beforeitemshow:function(t){le(t.target,this.clsActive)},itemshown:function(t){le(t.target,this.clsActivated)},itemhidden:function(t){he(t.target,this.clsActive,this.clsActivated)}}},kr={mixins:[Hn,Wn,Yi,xr],functional:!0,props:{delayControls:Number,preload:Number,videoAutoplay:Boolean,template:String},data:function(){return{preload:1,videoAutoplay:!1,delayControls:3e3,items:[],cls:"uk-open",clsPage:"uk-lightbox-page",selList:".uk-lightbox-items",attrItem:"uk-lightbox-item",selClose:".uk-close-large",pauseOnHover:!1,velocity:2,Animations:vr,template:'<div class="uk-lightbox uk-overflow-hidden"> <ul class="uk-lightbox-items"></ul> <div class="uk-lightbox-toolbar uk-position-top uk-text-right uk-transition-slide-top uk-transition-opaque"> <button class="uk-lightbox-toolbar-icon uk-close-large" type="button" uk-close></button> </div> <a class="uk-lightbox-button uk-position-center-left uk-position-medium uk-transition-fade" href="#" uk-slidenav-previous uk-lightbox-item="previous"></a> <a class="uk-lightbox-button uk-position-center-right uk-position-medium uk-transition-fade" href="#" uk-slidenav-next uk-lightbox-item="next"></a> <div class="uk-lightbox-toolbar uk-lightbox-caption uk-position-bottom uk-text-center uk-transition-slide-bottom uk-transition-opaque"></div> </div>'}},created:function(){var t=this;this.$mount(Kt(this.container,this.template)),this.caption=Be(".uk-lightbox-caption",this.$el),this.items.forEach(function(){return Kt(t.list,"<li></li>")})},events:[{name:hi+" "+li+" keydown",handler:"showControls"},{name:"click",self:!0,delegate:function(){return this.slidesSelector},handler:function(t){t.preventDefault(),this.hide()}},{name:"shown",self:!0,handler:"showControls"},{name:"hide",self:!0,handler:function(){this.hideControls(),he(this.slides,this.clsActive),_e.stop(this.slides)}},{name:"keyup",el:document,handler:function(t){if(this.isToggled(this.$el))switch(t.keyCode){case 37:this.show("previous");break;case 39:this.show("next")}}},{name:"beforeitemshow",handler:function(t){this.isToggled()||(this.preventCatch=!0,t.preventDefault(),this.toggleNow(this.$el,!0),this.animation=vr.scale,he(t.target,this.clsActive),this.stack.splice(1,0,this.index))}},{name:"itemshow",handler:function(t){var e=qt(t.target),i=this.getItem(e).caption;be(this.caption,"display",i?"":"none"),Jt(this.caption,i);for(var n=0;n<=this.preload;n++)this.loadItem(this.getIndex(e+n)),this.loadItem(this.getIndex(e-n))}},{name:"itemshown",handler:function(){this.preventCatch=!1}},{name:"itemload",handler:function(t,r){var o,s=this,e=r.source,i=r.type,n=r.alt;if(this.setItem(r,"<span uk-spinner></span>"),e)if("image"===i||e.match(/\.(jp(e)?g|png|gif|svg)($|\?)/i))Vt(e).then(function(t){return s.setItem(r,'<img width="'+t.width+'" height="'+t.height+'" src="'+e+'" alt="'+(n||"")+'">')},function(){return s.setError(r)});else if("video"===i||e.match(/\.(mp4|webm|ogv)($|\?)/i)){var a=Be("<video controls playsinline"+(r.poster?' poster="'+r.poster+'"':"")+' uk-video="'+this.videoAutoplay+'"></video>');Z(a,"src",e),Tt(a,"error",function(){return s.setError(r)}),Tt(a,"loadedmetadata",function(){Z(a,{width:a.videoWidth,height:a.videoHeight}),s.setItem(r,a)})}else if("iframe"===i||e.match(/\.(html|php)($|\?)/i))this.setItem(r,'<iframe class="uk-lightbox-iframe" src="'+e+'" frameborder="0" allowfullscreen></iframe>');else if(o=e.match(/\/\/.*?youtube(-nocookie)?\.[a-z]+\/watch\?v=([^&\s]+)/)||e.match(/()youtu\.be\/(.*)/)){var l=o[2],h=function(t,e){return void 0===t&&(t=640),void 0===e&&(e=450),s.setItem(r,$r("https://www.youtube"+(o[1]||"")+".com/embed/"+l,t,e,s.videoAutoplay))};Vt("https://img.youtube.com/vi/"+l+"/maxresdefault.jpg").then(function(t){var e=t.width,i=t.height;120===e&&90===i?Vt("https://img.youtube.com/vi/"+l+"/0.jpg").then(function(t){var e=t.width,i=t.height;return h(e,i)},h):h(e,i)},h)}else(o=e.match(/(\/\/.*?)vimeo\.[a-z]+\/([0-9]+).*?/))&&Ft("https://vimeo.com/api/oembed.json?maxwidth=1920&url="+encodeURI(e),{responseType:"json",withCredentials:!1}).then(function(t){var e=t.response,i=e.height,n=e.width;return s.setItem(r,$r("https://player.vimeo.com/video/"+o[2],n,i,s.videoAutoplay))},function(){return s.setError(r)})}}],methods:{loadItem:function(t){void 0===t&&(t=this.index);var e=this.getItem(t);e.content||_t(this.$el,"itemload",[e])},getItem:function(t){return void 0===t&&(t=this.index),this.items[t]||{}},setItem:function(t,e){Y(t,{content:e});var i=Jt(this.slides[this.items.indexOf(t)],e);_t(this.$el,"itemloaded",[this,i]),this.$update(i)},setError:function(t){this.setItem(t,'<span uk-icon="icon: bolt; ratio: 2"></span>')},showControls:function(){clearTimeout(this.controlsTimer),this.controlsTimer=setTimeout(this.hideControls,this.delayControls),le(this.$el,"uk-active","uk-transition-active")},hideControls:function(){he(this.$el,"uk-active","uk-transition-active")}}};function $r(t,e,i,n){return'<iframe src="'+t+'" width="'+e+'" height="'+i+'" style="max-width: 100%; box-sizing: border-box;" frameborder="0" allowfullscreen uk-video="autoplay: '+n+'" uk-responsive></iframe>'}var Ir,Sr={install:function(t,e){t.lightboxPanel||t.component("lightboxPanel",kr);Y(e.props,t.component("lightboxPanel").options.props)},attrs:!0,props:{toggle:String},data:{toggle:"a"},computed:{toggles:function(t,e){return ze(t.toggle,e)}},disconnected:function(){this._destroy()},events:[{name:"click",delegate:function(){return this.toggle+":not(.uk-disabled)"},handler:function(t){t.preventDefault(),t.current.blur(),this.show(qt(this.toggles,t.current))}}],update:function(t){var e,i;(t.toggles=this.panel&&t.toggles||this.toggles,this.panel&&(e=t.toggles,i=this.toggles,e.length!==i.length||!e.every(function(t,e){return t===i[e]})))&&(t.toggles=this.toggles,this._destroy(),this._init())},methods:{_init:function(){return this.panel=this.panel||this.$create("lightboxPanel",Y({},this.$props,{items:this.toggles.reduce(function(t,i){return t.push(["href","caption","type","poster","alt"].reduce(function(t,e){return t["href"===e?"source":e]=it(i,e),t},{})),t},[])}))},_destroy:function(){this.panel&&(this.panel.$destroy(!0),this.panel=null)},show:function(t){return this.panel||this._init(),this.panel.show(t)},hide:function(){return this.panel&&this.panel.hide()}}};var Tr={},Er={functional:!0,args:["message","status"],data:{message:"",status:"",timeout:5e3,group:null,pos:"top-center",clsClose:"uk-notification-close",clsMsg:"uk-notification-message"},install:function(r){r.notification.closeAll=function(i,n){ae(document.body,function(t){var e=r.getComponent(t,"notification");!e||i&&i!==e.group||e.close(n)})}},created:function(){Tr[this.pos]||(Tr[this.pos]=Kt(this.$container,'<div class="uk-notification uk-notification-'+this.pos+'"></div>'));var t=be(Tr[this.pos],"display","block");this.$mount(Kt(t,'<div class="'+this.clsMsg+(this.status?" "+this.clsMsg+"-"+this.status:"")+'"> <a href="#" class="'+this.clsClose+'" data-uk-close></a> <div>'+this.message+"</div> </div>"))},ready:function(){var t=this,e=P(be(this.$el,"marginBottom"));_e.start(be(this.$el,{opacity:0,marginTop:-this.$el.offsetHeight,marginBottom:0}),{opacity:1,marginTop:0,marginBottom:e}).then(function(){t.timeout&&(t.timer=setTimeout(t.close,t.timeout))})},events:(Ir={click:function(t){mt(t.target,'a[href="#"]')&&t.preventDefault(),this.close()}},Ir[ui]=function(){this.timer&&clearTimeout(this.timer)},Ir[di]=function(){this.timeout&&(this.timer=setTimeout(this.close,this.timeout))},Ir),methods:{close:function(t){var e=this,i=function(){_t(e.$el,"close",[e]),te(e.$el),Tr[e.pos].children.length||be(Tr[e.pos],"display","none")};this.timer&&clearTimeout(this.timer),t?i():_e.start(this.$el,{opacity:0,marginTop:-this.$el.offsetHeight,marginBottom:0}).then(i)}}};var Cr=["x","y","bgx","bgy","rotate","scale","color","backgroundColor","borderColor","opacity","blur","hue","grayscale","invert","saturate","sepia","fopacity"],_r={props:Cr.reduce(function(t,e){return t[e]="list",t},{media:"media"}),data:Cr.reduce(function(t,e){return t[e]=void 0,t},{media:!1}),computed:{props:function(f,p){var m=this;return Cr.reduce(function(t,e){if(D(f[e]))return t;var i,n,r,o=e.match(/color/i),s=o||"opacity"===e,a=f[e].slice(0);s&&be(p,e,""),a.length<2&&a.unshift(("scale"===e?1:s?be(p,e):0)||0);var l=b(a.join(""),"%")?"%":"px";if(o){var h=p.style.color;a=a.map(function(t){return be(be(p,"color",t),"color").split(/[(),]/g).slice(1,-1).concat(1).slice(0,4).map(function(t){return P(t)})}),p.style.color=h}else a=a.map(P);if(e.match(/^bg/))if(be(p,"background-position-"+e[2],""),n=be(p,"backgroundPosition").split(" ")["x"===e[2]?0:1],m.covers){var c=Math.min.apply(Math,a),u=Math.max.apply(Math,a),d=a.indexOf(c)<a.indexOf(u);r=u-c,a=a.map(function(t){return t-(d?c:u)}),i=(d?-r:0)+"px"}else i=n;return t[e]={steps:a,unit:l,pos:i,bgPos:n,diff:r},t},{})},bgProps:function(){var e=this;return["bgx","bgy"].filter(function(t){return t in e.props})},covers:function(t,e){return n=(i=e).style.backgroundSize,r="cover"===be(be(i,"backgroundSize",""),"backgroundSize"),i.style.backgroundSize=n,r;var i,n,r}},disconnected:function(){delete this._image},update:[{read:function(e){var i=this;if(e.active=!this.media||window.matchMedia(this.media).matches,e.image&&(e.image.dimEl={width:this.$el.offsetWidth,height:this.$el.offsetHeight}),!("image"in e)&&this.covers&&this.bgProps.length){var t=be(this.$el,"backgroundImage").replace(/^none|url\(["']?(.+?)["']?\)$/,"$1");t&&(e.image=!1,Vt(t).then(function(t){e.image={width:t.naturalWidth,height:t.naturalHeight},i.$emit()}))}},write:function(t){var l=this,h=t.image,e=t.active;if(h)if(e){var c=h.dimEl,u=G.cover(h,c);this.bgProps.forEach(function(t){var e=l.props[t],i=e.diff,n=e.bgPos,r=e.steps,o="bgy"===t?"height":"width",s=u[o]-c[o];if(n.match(/%$|0px/)){if(s<i)c[o]=u[o]+i-s;else if(i<s){var a=parseFloat(n);a&&(l.props[t].steps=r.map(function(t){return t-(s-i)/(100/a)}))}u=G.cover(h,c)}}),be(this.$el,{backgroundSize:u.width+"px "+u.height+"px",backgroundRepeat:"no-repeat"})}else be(this.$el,{backgroundSize:"",backgroundRepeat:""})},events:["load","resize"]}],methods:{reset:function(){var i=this;R(this.getCss(0),function(t,e){return be(i.$el,e,"")})},getCss:function(p){var m=this.props,g=!1;return Object.keys(m).reduce(function(t,e){var i=m[e],n=i.steps,r=i.unit,o=i.pos,s=Nr(n,p);switch(e){case"x":case"y":if(g)break;var a=["x","y"].map(function(t){return e===t?s+r:m[t]?Nr(m[t].steps,p)+m[t].unit:0}),l=a[0],h=a[1];g=t.transform+=" translate3d("+l+", "+h+", 0)";break;case"rotate":t.transform+=" rotate("+s+"deg)";break;case"scale":t.transform+=" scale("+s+")";break;case"bgy":case"bgx":t["background-position-"+e[2]]="calc("+o+" + "+(s+r)+")";break;case"color":case"backgroundColor":case"borderColor":var c=Ar(n,p),u=c[0],d=c[1],f=c[2];t[e]="rgba("+u.map(function(t,e){return t+=f*(d[e]-t),3===e?P(t):parseInt(t,10)}).join(",")+")";break;case"blur":t.filter+=" blur("+s+"px)";break;case"hue":t.filter+=" hue-rotate("+s+"deg)";break;case"fopacity":t.filter+=" opacity("+s+"%)";break;case"grayscale":case"invert":case"saturate":case"sepia":t.filter+=" "+e+"("+s+"%)";break;default:t[e]=s}return t},{transform:"",filter:""})}}};function Ar(t,e){var i=t.length-1,n=Math.min(Math.floor(i*e),i-1),r=t.slice(n,n+2);return r.push(1===e?1:e%(1/i)*i),r}function Nr(t,e){var i=Ar(t,e),n=i[0],r=i[1],o=i[2];return(O(n)?n+Math.abs(n-r)*o*(n<r?1:-1):+r).toFixed(2)}var Or={mixins:[_r],props:{target:String,viewport:Number,easing:Number},data:{target:!1,viewport:1,easing:1},computed:{target:function(t,e){var i=t.target;return i&&nt(i,e)||e}},update:[{read:function(t){var e,i;return{prev:t.percent,percent:(e=Ze(this.target)/(this.viewport||1),i=this.easing,U(e*(1-(i-i*e))))}},write:function(t,e){var i=t.prev,n=t.percent,r=t.active;"scroll"!==e.type&&(i=!1),r?i!==n&&be(this.$el,this.getCss(n)):this.reset()},events:["scroll","load","resize"]}]};var Mr={update:[{write:function(){if(!this.stack.length&&!this.dragging){var t=this.getValidIndex();delete this.index,he(this.slides,this.clsActive,this.clsActivated),this.show(t)}},events:["load","resize"]}]};function Dr(t,e,i){var n,r=Pr(t,e);return i?r-(n=t,e.offsetWidth/2-n.offsetWidth/2):Math.min(r,Br(e))}function Br(t){return Math.max(0,zr(t)-t.offsetWidth)}function zr(t){return Wr(t).reduce(function(t,e){return e.offsetWidth+t},0)}function Pr(t,e){return(t.offsetLeft+(ri?t.offsetWidth-e.offsetWidth:0))*(ri?-1:1)}function Hr(t,e,i){_t(t,At(e,!1,!1,i))}function Wr(t){return L(t.children)}var Lr={mixins:[Vi,br,Mr],props:{center:Boolean,sets:Boolean},data:{center:!1,sets:!1,attrItem:"uk-slider-item",selList:".uk-slider-items",selNav:".uk-slider-nav",clsContainer:"uk-slider-container",Transitioner:function(r,n,o,t){var e=t.center,s=t.easing,a=t.list,l=new Pt,i=r?Dr(r,a,e):Dr(n,a,e)+n.offsetWidth*o,h=n?Dr(n,a,e):i+r.offsetWidth*o*(ri?-1:1);return{dir:o,show:function(t,e,i){void 0===e&&(e=0);var n=i?"linear":s;return t-=Math.round(t*U(e,-1,1)),this.translate(e),r&&this.updateTranslates(),e=r?e:U(e,0,1),Hr(this.getItemIn(),"itemin",{percent:e,duration:t,timing:n,dir:o}),r&&Hr(this.getItemIn(!0),"itemout",{percent:1-e,duration:t,timing:n,dir:o}),_e.start(a,{transform:mr(-h*(ri?-1:1),"px")},t,n).then(l.resolve,X),l.promise},stop:function(){return _e.stop(a)},cancel:function(){_e.cancel(a)},reset:function(){be(a,"transform","")},forward:function(t,e){return void 0===e&&(e=this.percent()),_e.cancel(a),this.show(t,e,!0)},translate:function(t){var e=this.getDistance()*o*(ri?-1:1);be(a,"transform",mr(U(e-e*t-h,-zr(a),a.offsetWidth)*(ri?-1:1),"px")),this.updateTranslates(),r&&(t=U(t,-1,1),Hr(this.getItemIn(),"itemtranslatein",{percent:t,dir:o}),Hr(this.getItemIn(!0),"itemtranslateout",{percent:1-t,dir:o}))},percent:function(){return Math.abs((be(a,"transform").split(",")[4]*(ri?-1:1)+i)/(h-i))},getDistance:function(){return Math.abs(h-i)},getItemIn:function(t){void 0===t&&(t=!1);var e=this.getActives(),i=q(Wr(a),"offsetLeft"),n=qt(i,e[0<o*(t?-1:1)?e.length-1:0]);return~n&&i[n+(r&&!t?o:0)]},getActives:function(){var i=Dr(r||n,a,e);return q(Wr(a).filter(function(t){var e=Pr(t,a);return i<=e&&e+t.offsetWidth<=a.offsetWidth+i}),"offsetLeft")},updateTranslates:function(){var i=this.getActives();Wr(a).forEach(function(t){var e=b(i,t);Hr(t,"itemtranslate"+(e?"in":"out"),{percent:e?1:0,dir:t.offsetLeft<=n.offsetLeft?1:-1})})}}}},computed:{avgWidth:function(){return zr(this.list)/this.length},finite:function(t){return t.finite||zr(this.list)<this.list.offsetWidth+Wr(this.list).reduce(function(t,e){return Math.max(t,e.offsetWidth)},0)+this.center},maxIndex:function(){if(!this.finite||this.center&&!this.sets)return this.length-1;if(this.center)return this.sets[this.sets.length-1];be(this.slides,"order","");for(var t=Br(this.list),e=this.length;e--;)if(Pr(this.list.children[e],this.list)<t)return Math.min(e+1,this.length-1);return 0},sets:function(t){var o=this,e=t.sets,s=this.list.offsetWidth/(this.center?2:1),a=0,l=s,h=0;return(e=e&&this.slides.reduce(function(t,e,i){var n=Le(e).width;if(a<h+n&&(!o.center&&i>o.maxIndex&&(i=o.maxIndex),!b(t,i))){var r=o.slides[i+1];o.center&&r&&n<l-Le(r).width/2?l-=n:(l=s,t.push(i),a=h+s+(o.center?n/2:0))}return h+=n,t},[]))&&e.length&&e},transitionOptions:function(){return{center:this.center,list:this.list}}},connected:function(){fe(this.$el,this.clsContainer,!Be("."+this.clsContainer,this.$el))},update:{write:function(){var i=this;ze("["+this.attrItem+"],[data-"+this.attrItem+"]",this.$el).forEach(function(t){var e=it(t,i.attrItem);i.maxIndex&&fe(t,"uk-hidden",M(e)&&(i.sets&&!b(i.sets,P(e))||e>i.maxIndex))})},events:["load","resize"]},events:{beforeitemshow:function(t){!this.dragging&&this.sets&&this.stack.length<2&&!b(this.sets,this.index)&&(this.index=this.getValidIndex());var e=Math.abs(this.index-this.prevIndex+(0<this.dir&&this.index<this.prevIndex||this.dir<0&&this.index>this.prevIndex?(this.maxIndex+1)*this.dir:0));if(!this.dragging&&1<e){for(var i=0;i<e;i++)this.stack.splice(1,0,0<this.dir?"next":"previous");t.preventDefault()}else this.duration=yr(this.avgWidth/this.velocity)*((this.dir<0||!this.slides[this.prevIndex]?this.slides[this.index]:this.slides[this.prevIndex]).offsetWidth/this.avgWidth),this.reorder()},itemshow:function(){!D(this.prevIndex)&&le(this._getTransitioner().getItemIn(),this.clsActive)},itemshown:function(){var e=this,i=this._getTransitioner(this.index).getActives();this.slides.forEach(function(t){return fe(t,e.clsActive,b(i,t))}),(!this.sets||b(this.sets,P(this.index)))&&this.slides.forEach(function(t){return fe(t,e.clsActivated,b(i,t))})}},methods:{reorder:function(){var i=this;if(be(this.slides,"order",""),!this.finite){var n=0<this.dir&&this.slides[this.prevIndex]?this.prevIndex:this.index;if(this.slides.forEach(function(t,e){return be(t,"order",0<i.dir&&e<n?1:i.dir<0&&e>=i.index?-1:"")}),this.center)for(var t=this.slides[n],e=this.list.offsetWidth/2-t.offsetWidth/2,r=0;0<e;){var o=i.getIndex(--r+n,n),s=i.slides[o];be(s,"order",n<o?-2:-1),e-=s.offsetWidth}}},getValidIndex:function(t,e){var i;if(void 0===t&&(t=this.index),void 0===e&&(e=this.prevIndex),t=this.getIndex(t,e),!this.sets)return t;do{if(b(this.sets,t))return t;i=t,t=this.getIndex(t+this.dir,e)}while(t!==i);return t}}},jr={mixins:[_r],data:{selItem:"!li"},computed:{item:function(t,e){return nt(t.selItem,e)}},events:[{name:"itemshown",self:!0,el:function(){return this.item},handler:function(){be(this.$el,this.getCss(.5))}},{name:"itemin itemout",self:!0,el:function(){return this.item},handler:function(t){var e=t.type,i=t.detail,n=i.percent,r=i.duration,o=i.timing,s=i.dir;_e.cancel(this.$el),be(this.$el,this.getCss(Vr(e,s,n))),_e.start(this.$el,this.getCss(Fr(e)?.5:0<s?1:0),r,o).catch(X)}},{name:"transitioncanceled transitionend",self:!0,el:function(){return this.item},handler:function(){_e.cancel(this.$el)}},{name:"itemtranslatein itemtranslateout",self:!0,el:function(){return this.item},handler:function(t){var e=t.type,i=t.detail,n=i.percent,r=i.dir;_e.cancel(this.$el),be(this.$el,this.getCss(Vr(e,r,n)))}}]};function Fr(t){return u(t,"in")}function Vr(t,e,i){return i/=2,Fr(t)?e<0?1-i:i:e<0?i:1-i}var Yr,Rr,qr=Y({},fr,{fade:{show:function(){return[{opacity:0,zIndex:0},{zIndex:-1}]},percent:function(t){return 1-be(t,"opacity")},translate:function(t){return[{opacity:1-t,zIndex:0},{zIndex:-1}]}},scale:{show:function(){return[{opacity:0,transform:gr(1.5),zIndex:0},{zIndex:-1}]},percent:function(t){return 1-be(t,"opacity")},translate:function(t){return[{opacity:1-t,transform:gr(1+.5*t),zIndex:0},{zIndex:-1}]}},pull:{show:function(t){return t<0?[{transform:mr(30),zIndex:-1},{transform:mr(),zIndex:0}]:[{transform:mr(-100),zIndex:0},{transform:mr(),zIndex:-1}]},percent:function(t,e,i){return i<0?1-pr(e):pr(t)},translate:function(t,e){return e<0?[{transform:mr(30*t),zIndex:-1},{transform:mr(-100*(1-t)),zIndex:0}]:[{transform:mr(100*-t),zIndex:0},{transform:mr(30*(1-t)),zIndex:-1}]}},push:{show:function(t){return t<0?[{transform:mr(100),zIndex:0},{transform:mr(),zIndex:-1}]:[{transform:mr(-30),zIndex:-1},{transform:mr(),zIndex:0}]},percent:function(t,e,i){return 0<i?1-pr(e):pr(t)},translate:function(t,e){return e<0?[{transform:mr(100*t),zIndex:0},{transform:mr(-30*(1-t)),zIndex:-1}]:[{transform:mr(-30*t),zIndex:-1},{transform:mr(100*(1-t)),zIndex:0}]}}}),Ur={mixins:[Vi,xr,Mr],props:{ratio:String,minHeight:Boolean,maxHeight:Boolean},data:{ratio:"16:9",minHeight:!1,maxHeight:!1,selList:".uk-slideshow-items",attrItem:"uk-slideshow-item",selNav:".uk-slideshow-nav",Animations:qr},update:{read:function(){var t=this.ratio.split(":").map(Number),e=t[0],i=t[1];return i=i*this.$el.offsetWidth/e,this.minHeight&&(i=Math.max(this.minHeight,i)),this.maxHeight&&(i=Math.min(this.maxHeight,i)),{height:i}},write:function(t){var e=t.height;Ve(this.list,Math.floor(e))},events:["load","resize"]}},Xr={mixins:[Vi,or],props:{group:String,threshold:Number,clsItem:String,clsPlaceholder:String,clsDrag:String,clsDragState:String,clsBase:String,clsNoDrag:String,clsEmpty:String,clsCustom:String,handle:String},data:{group:!1,threshold:5,clsItem:"uk-sortable-item",clsPlaceholder:"uk-sortable-placeholder",clsDrag:"uk-sortable-drag",clsDragState:"uk-drag",clsBase:"uk-sortable",clsNoDrag:"uk-sortable-nodrag",clsEmpty:"uk-sortable-empty",clsCustom:"",handle:!1},init:function(){var o=this;["init","start","move","end"].forEach(function(t){var r=o[t];o[t]=function(t){o.scrollY=window.pageYOffset;var e=Bi(t),i=e.x,n=e.y;o.pos={x:i,y:n},r(t)}})},events:(Yr={},Yr[li]="init",Yr),update:{write:function(){if(this.clsEmpty&&fe(this.$el,this.clsEmpty,!this.$el.children.length),this.drag){Le(this.drag,{top:this.pos.y+this.origin.top,left:this.pos.x+this.origin.left});var t,e=Le(this.drag).top,i=e+this.drag.offsetHeight;0<e&&e<this.scrollY?t=this.scrollY-5:i<Ve(document)&&i>Ve(window)+this.scrollY&&(t=this.scrollY+5),t&&setTimeout(function(){return Qe(window,t)},5)}}},methods:{init:function(t){var e=t.target,i=t.button,n=t.defaultPrevented,r=L(this.$el.children).filter(function(t){return St(e,t)})[0];!r||$t(t.target)||this.handle&&!St(e,this.handle)||0<i||St(e,"."+this.clsNoDrag)||n||(t.preventDefault(),this.touched=[this],this.placeholder=r,this.origin=Y({target:e,index:qt(r)},this.pos),Tt(document,hi,this.move),Tt(document,ci,this.end),Tt(window,"scroll",this.scroll),this.threshold||this.start(t))},start:function(t){this.drag=Kt(this.$container,this.placeholder.outerHTML.replace(/^<li/i,"<div").replace(/li>$/i,"div>")),be(this.drag,Y({boxSizing:"border-box",width:this.placeholder.offsetWidth,height:this.placeholder.offsetHeight},be(this.placeholder,["paddingLeft","paddingRight","paddingTop","paddingBottom"]))),Z(this.drag,"uk-no-boot",""),le(this.drag,this.clsDrag,this.clsCustom),Ve(this.drag.firstElementChild,Ve(this.placeholder.firstElementChild));var e=Le(this.placeholder),i=e.left,n=e.top;Y(this.origin,{left:i-this.pos.x,top:n-this.pos.y}),le(this.placeholder,this.clsPlaceholder),le(this.$el.children,this.clsItem),le(document.documentElement,this.clsDragState),_t(this.$el,"start",[this,this.placeholder]),this.move(t)},move:function(t){if(this.drag){this.$emit();var e="mousemove"===t.type?t.target:document.elementFromPoint(this.pos.x-document.body.scrollLeft,this.pos.y-document.body.scrollTop),i=this.getSortable(e),n=this.getSortable(this.placeholder),r=i!==n;if(i&&!St(e,this.placeholder)&&(!r||i.group&&i.group===n.group)){if(e=i.$el===e.parentNode&&e||L(i.$el.children).filter(function(t){return St(e,t)})[0],r)n.remove(this.placeholder);else if(!e)return;i.insert(this.placeholder,e),b(this.touched,i)||this.touched.push(i)}}else(Math.abs(this.pos.x-this.origin.x)>this.threshold||Math.abs(this.pos.y-this.origin.y)>this.threshold)&&this.start(t)},scroll:function(){var t=window.pageYOffset;t!==this.scrollY&&(this.pos.y+=t-this.scrollY,this.scrollY=t,this.$emit())},end:function(t){if(Et(document,hi,this.move),Et(document,ci,this.end),Et(window,"scroll",this.scroll),this.drag){Bt();var e=this.getSortable(this.placeholder);this===e?this.origin.index!==qt(this.placeholder)&&_t(this.$el,"moved",[this,this.placeholder]):(_t(e.$el,"added",[e,this.placeholder]),_t(this.$el,"removed",[this,this.placeholder])),_t(this.$el,"stop",[this,this.placeholder]),te(this.drag),this.drag=null;var i=this.touched.map(function(t){return t.clsPlaceholder+" "+t.clsItem}).join(" ");this.touched.forEach(function(t){return he(t.$el.children,i)}),he(document.documentElement,this.clsDragState)}else"mouseup"!==t.type&&St(t.target,"a[href]")&&(location.href=mt(t.target,"a[href]").href)},insert:function(i,n){var r=this;le(this.$el.children,this.clsItem);var t=function(){var t,e;n?!St(i,r.$el)||(e=n,(t=i).parentNode===e.parentNode&&qt(t)>qt(e))?Gt(n,i):Zt(n,i):Kt(r.$el,i)};this.animation?this.animate(t):t()},remove:function(t){St(t,this.$el)&&(this.animation?this.animate(function(){return te(t)}):te(t))},getSortable:function(t){return t&&(this.$getComponent(t,"sortable")||this.getSortable(t.parentNode))}}};var Jr=[],Kr={mixins:[Hn,Yi,Zi],args:"title",attrs:!0,props:{delay:Number,title:String},data:{pos:"top",title:"",delay:0,animation:["uk-animation-scale-up"],duration:100,cls:"uk-active",clsPos:"uk-tooltip"},beforeConnect:function(){this._hasTitle=Q(this.$el,"title"),Z(this.$el,{title:"","aria-expanded":!1})},disconnected:function(){this.hide(),Z(this.$el,{title:this._hasTitle?this.title:null,"aria-expanded":null})},methods:{show:function(){var e=this;b(Jr,this)||(Jr.forEach(function(t){return t.hide()}),Jr.push(this),this._unbind=Tt(document,"click",function(t){return!St(t.target,e.$el)&&e.hide()}),clearTimeout(this.showTimer),this.tooltip=Kt(this.container,'<div class="'+this.clsPos+'" aria-hidden><div class="'+this.clsPos+'-inner">'+this.title+"</div></div>"),Z(this.$el,"aria-expanded",!0),this.positionAt(this.tooltip,this.$el),this.origin="y"===this.getAxis()?Ke(this.dir)+"-"+this.align:this.align+"-"+Ke(this.dir),this.showTimer=setTimeout(function(){e.toggleElement(e.tooltip,!0),e.hideTimer=setInterval(function(){xt(e.$el)||e.hide()},150)},this.delay))},hide:function(){var t=Jr.indexOf(this);!~t||ft(this.$el,"input")&&this.$el===document.activeElement||(Jr.splice(t,1),clearTimeout(this.showTimer),clearInterval(this.hideTimer),Z(this.$el,"aria-expanded",!1),this.toggleElement(this.tooltip,!1),this.tooltip&&te(this.tooltip),this.tooltip=!1,this._unbind())}},events:(Rr={},Rr["focus "+ui+" "+li]=function(t){t.type===li&&Di(t)||this.show()},Rr.blur="hide",Rr[di]=function(t){Di(t)||this.hide()},Rr)},Gr={props:{allow:String,clsDragover:String,concurrent:Number,maxSize:Number,method:String,mime:String,msgInvalidMime:String,msgInvalidName:String,msgInvalidSize:String,multiple:Boolean,name:String,params:Object,type:String,url:String},data:{allow:!1,clsDragover:"uk-dragover",concurrent:1,maxSize:0,method:"POST",mime:!1,msgInvalidMime:"Invalid File Type: %s",msgInvalidName:"Invalid File Name: %s",msgInvalidSize:"Invalid File Size: %s Kilobytes Max",multiple:!1,name:"files[]",params:{},type:"",url:"",abort:X,beforeAll:X,beforeSend:X,complete:X,completeAll:X,error:X,fail:X,load:X,loadEnd:X,loadStart:X,progress:X},events:{change:function(t){ft(t.target,'input[type="file"]')&&(t.preventDefault(),t.target.files&&this.upload(t.target.files),t.target.value="")},drop:function(t){Qr(t);var e=t.dataTransfer;e&&e.files&&(he(this.$el,this.clsDragover),this.upload(e.files))},dragenter:function(t){Qr(t)},dragover:function(t){Qr(t),le(this.$el,this.clsDragover)},dragleave:function(t){Qr(t),he(this.$el,this.clsDragover)}},methods:{upload:function(t){var n=this;if(t.length){_t(this.$el,"upload",[t]);for(var e=0;e<t.length;e++){if(n.maxSize&&1e3*n.maxSize<t[e].size)return void n.fail(n.msgInvalidSize.replace("%s",n.maxSize));if(n.allow&&!Zr(n.allow,t[e].name))return void n.fail(n.msgInvalidName.replace("%s",n.allow));if(n.mime&&!Zr(n.mime,t[e].type))return void n.fail(n.msgInvalidMime.replace("%s",n.mime))}this.multiple||(t=[t[0]]),this.beforeAll(this,t);var r=function(t,e){for(var i=[],n=0;n<t.length;n+=e){for(var r=[],o=0;o<e;o++)r.push(t[n+o]);i.push(r)}return i}(t,this.concurrent),o=function(t){var e=new FormData;for(var i in t.forEach(function(t){return e.append(n.name,t)}),n.params)e.append(i,n.params[i]);Ft(n.url,{data:e,method:n.method,responseType:n.type,beforeSend:function(t){var e=t.xhr;e.upload&&Tt(e.upload,"progress",n.progress),["loadStart","load","loadEnd","abort"].forEach(function(t){return Tt(e,t.toLowerCase(),n[t])}),n.beforeSend(t)}}).then(function(t){n.complete(t),r.length?o(r.shift()):n.completeAll(t)},function(t){return n.error(t.message)})};o(r.shift())}}}};function Zr(t,e){return e.match(new RegExp("^"+t.replace(/\//g,"\\/").replace(/\*\*/g,"(\\/[^\\/]+)*").replace(/\*/g,"[^\\/]+").replace(/((?!\\))\?/g,"$1.")+"$","i"))}function Qr(t){t.preventDefault(),t.stopPropagation()}return Fi.component("countdown",ir),Fi.component("filter",hr),Fi.component("lightbox",Sr),Fi.component("lightboxPanel",kr),Fi.component("notification",Er),Fi.component("parallax",Or),Fi.component("slider",Lr),Fi.component("sliderParallax",jr),Fi.component("slideshow",Ur),Fi.component("slideshowParallax",jr),Fi.component("sortable",Xr),Fi.component("tooltip",Kr),Fi.component("upload",Gr),function(o){var s=o.connect,a=o.disconnect;function t(){l(document.body,s),fi.flush(),new MutationObserver(function(t){return t.forEach(e)}).observe(document,{childList:!0,subtree:!0,characterData:!0,attributes:!0}),o._initialized=!0}function e(t){var e=t.target;("attributes"!==t.type?function(t){for(var e=t.addedNodes,i=t.removedNodes,n=0;n<e.length;n++)l(e[n],s);for(var r=0;r<i.length;r++)l(i[r],a);return!0}(t):function(t){var e=t.target,i=t.attributeName;if("href"===i)return!0;var n=Pi(i);if(n&&n in o){if(Q(e,i))return o[n](e),!0;var r=o.getComponent(e,n);return r?(r.$destroy(),!0):void 0}}(t))&&o.update(e)}function l(t,e){if(1===t.nodeType&&!Q(t,"uk-no-boot"))for(e(t),t=t.firstElementChild;t;){var i=t.nextElementSibling;l(t,e),t=i}}"MutationObserver"in window&&(document.body?t():new MutationObserver(function(){document.body&&(this.disconnect(),t())}).observe(document,{childList:!0,subtree:!0}))}(Fi),Fi});
from django.core.exceptions import ValidationError
from wagtail import blocks
from wagtail.images.blocks import ImageChooserBlock


class ImageBlock(blocks.StructBlock):
    image = ImageChooserBlock(required=True)
    caption = blocks.TextBlock(required=True, help_text="Caption must be at least 30 words for detailed description")
    
    def clean(self, value):
        result = super().clean(value)
        
        # Check caption word count (when used in a gallery)
        caption = result.get('caption', '')
        if caption:
            word_count = len(caption.split())
            if word_count < 30:
                raise ValidationError(
                    f"Caption must be at least 30 words. Current count: {word_count} words."
                )
        
        return result
    
    class Meta:
        icon = 'image'
        template = 'blocks/image_block.html'
        label = 'Image'
from django import forms
from django.db import models
from modelcluster.fields import ParentalManyToManyField
from wagtail.admin.panels import FieldPanel, MultiFieldPanel
from wagtail.models import Page
from wagtail.search import index

from college.blocks.stream_fields import get_college_content_field
from college.models.college_category import CollegeCategory

"""Model for college posts"""


class CollegePage(Page):
    parent_page_types = ['home.HomePage']
    subpage_types = []


    """Analytics"""
    view_count = models.IntegerField(default=0)

    """College Fields"""
    short_title = models.CharField(max_length=512, default='')
    short_description = models.CharField(max_length=70, default='')
    body = get_college_content_field()

    featured_image = models.ForeignKey(
        'wagtailimages.Image',
        null=True,
        blank=False,
        on_delete=models.PROTECT,
        related_name='+'
    )

    categories = ParentalManyToManyField(CollegeCategory, blank=True, related_name='college_posts')

    keywords = models.Char<PERSON>ield(
        max_length=512,
        default="",
        blank=True,
        help_text="Keywords for the college post"
    )

    content_panels = Page.content_panels + [
        MultiFieldPanel([
            FieldPanel('slug'),
            FieldPanel('short_title'),
            FieldPanel('short_description'),
            FieldPanel('keywords'),
            FieldPanel('featured_image'),
        ], heading="College Information"),
        MultiFieldPanel([
            FieldPanel('body'),
        ], heading="Body"),
        MultiFieldPanel([
            FieldPanel('categories', widget=forms.CheckboxSelectMultiple),
        ], heading="College Categorization"),
    ]

    search_fields = Page.search_fields + [
        index.SearchField('short_title'),
        index.SearchField('short_description'),
        index.SearchField('keywords'),
    ]

    Page.promote_panels = []

    def serve(self, request, **kwargs):
        from django.db.models import F
        type(self).objects.filter(id=self.id).update(view_count=F('view_count') + 1)
        return super().serve(request)


    def save(self, *args, **kwargs):
        """Process and save the college post with automatic flagging"""
        is_new = self._state.adding
        """Save first w to avoid recursive save"""
        super().save(*args, **kwargs)



    def get_page_path(self):
        """Create URL path based on category hierarchy and page slug"""
        return  f'/{self.slug}-{self.id}.html'

    def get_url_parts(self, request=None):
        """Custom URL generation with category hierarchy"""
        url_parts = super().get_url_parts(request)
        if url_parts is None:
            return None

        site_id, root_url, _ = url_parts
        return site_id, root_url, self.get_page_path()



    def get_absolute_url(self):
        return self.url


    def get_url(self, request=None, current_site=None):
        """Override get_url to handle possible None values"""
        return f"/{self.slug}-{self.id}.html"


    class Meta:
        verbose_name = "College Post"
        verbose_name_plural = "College Posts"

    def __str__(self):
        return self.title

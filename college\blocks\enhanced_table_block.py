from wagtail.contrib.table_block.blocks import TableBlock as WagtailTableBlock


class EnhancedTableBlock(WagtailTableBlock):
    def __init__(self, **kwargs):
        table_options = {
            'minSpareRows': 0,
            'startRows': 4,
            'startCols': 4,
            'colHeaders': True,
            'rowHeaders': True,
            'contextMenu': True,
            'editor': 'text',
            'stretchH': 'all',
            'height': 216,
            'renderer': 'html',
            'autoColumnSize': False,
        }
        
        kwargs.setdefault('table_options', table_options)
        super().__init__(**kwargs)
    
    class Meta:
        icon = 'table'
        template = 'blocks/enhanced_table_block.html'
        label = 'Table'
from django.core.paginator import Paginator
from django.db import models
from django.http import Http404
from django.shortcuts import render
from django.utils.text import slugify
from wagtail.admin.panels import FieldPanel
from wagtail.models import Orderable
from wagtail.snippets.models import register_snippet


@register_snippet
class CollegeCategory(models.Model, Orderable):
    """Categories for college content"""
    name = models.CharField(max_length=100)
    slug = models.SlugField(unique=True, max_length=100)
    description = models.TextField(blank=True)

    panels = [
        FieldPanel('name'),
        FieldPanel('slug'),
        FieldPanel('description'),
    ]


    def __str__(self):
        return self.name

    class Meta:
        verbose_name_plural = "College categories"

    """
        Category management methods
    """


    def get_absolute_url(self):
        """Get URL for category page"""
        return f"/{self.slug}/"

    def get_posts(self):
        """Get all posts associated with this category"""
        from college.models import CollegePage
        return CollegePage.objects.live().filter(categories=self).distinct().order_by('-first_published_at')

    @classmethod
    def serve_category(cls, request, category_slug):
        """Render the category page for a given slug"""
        try:
            # Find the category by slug
            category = cls.objects.get(slug=category_slug)

            # Get posts for this category
            posts = category.get_posts()

            # Paginate the posts (30 per page)
            paginator = Paginator(posts, 30)
            page_number = request.GET.get('page')
            page_obj = paginator.get_page(page_number)

            # Render the category template
            return render(request, 'college/college_category_page.html', {
                'category': category,
                'posts': page_obj,
                'page_obj': page_obj,
            })
        except cls.DoesNotExist:
            raise Http404("Category does not exist")



/*  
    Template Name: Automobile
    Author: Themewinter
    Author URI: https://themeforest.net/user/themewinter
    Description: Automobile HTML Template
    Version: 1.0
*/
/* Table of Content
=================================================
1. Typography
2. Top Bar
3. Header
4. Global Style (body, link color, gap, ul, section-title, overlay etc)
*/
@import url("https://fonts.googleapis.com/css?family=Arimo:400,400i,700,700i");
@import url("https://fonts.googleapis.com/css?family=Heebo:400,500,700,800,900");
/* ============================== */
/* Typography
================================================== */
body {
  background: #f0f1f4;
  color: #8a8a8a;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
  font-size: 14px;
  line-height: 24px;
  font-family: "Arimo", sans-serif; }

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 700;
  color: #222222;
  font-family: "Heebo", sans-serif; }

h1 {
  font-size: 36px; }

h2 {
  font-size: 28px; }

h3 {
  font-size: 20px; }

h4 {
  font-size: 16px; }

/* Global styles
================================================== */
a,
a:active,
a:focus,
a:hover,
a:visited {
  text-decoration: none;
  outline: 0 solid; }

a:hover {
  color: #000; }

button:focus,
.btn:focus {
  outline: 0 solid;
  -webkit-box-shadow: none;
  box-shadow: none; }

ul {
  padding: 0;
  margin: 0;
  list-style: none; }

.pl-0 {
  padding-left: 0; }

.pr-0 {
  padding-right: 0; }

.pb-0 {
  padding-bottom: 0; }

.pt-0 {
  padding-top: 0; }

.p-0 {
  padding: 0; }

.mb-30 {
  margin-bottom: 30px; }

.mb-35 {
  margin-bottom: 35px; }

.mb-40 {
  margin-bottom: 40px; }

.mb-45 {
  margin-bottom: 45px; }

.mb-50 {
  margin-bottom: 50px; }

.mb-60 {
  margin-bottom: 60px; }

/*color setting --*/
.gr-color .post-tag .a {
  color: #4ab106; }

.bl-color .post-tag a {
  color: #007dff; }

.bl-light-color .post-tag a {
  color: #6200ee; }

.pink-color .post-tag a {
  color: #ff2dcb; }

.yal-color .post-tag a {
  color: #ff6e0d; }

/*---- cat color --*/
.gr-color-bg .post-cat {
  background: #4ab106; }

.bl-color-bg .post-cat {
  background: #007dff; }

.bl-light-color-bg .post-cat {
  background: #6200ee; }

.pink-color-bg .post-cat {
  background: #ff2dcb; }

.yal-color-bg .post-cat {
  background: #ff6e0d; }

/* defailt title setting */
.ts-title {
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 25px;
  position: relative; }
  .ts-title:before {
    position: absolute;
    left: -30px;
    top: 0;
    width: 3px;
    height: 100%;
    content: '';
    background: #d72924; }

.post-title {
  font-size: 15px;
  font-weight: 500;
  font-family: "Heebo", sans-serif;
  color: #232323; }
  .post-title.lg {
    font-size: 36px; }
  .post-title a {
    color: #232323; }
    .post-title a:hover {
      color: #d72924; }

#featured-slider .owl-nav {
  position: absolute;
  right: 0;
  top: 0; }
  #featured-slider .owl-nav .owl-prev:before {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 1px;
    height: 15px;
    background: #406a91;
    content: '';
    margin: auto; }
  #featured-slider .owl-nav .owl-prev,
  #featured-slider .owl-nav .owl-next {
    font-size: 14px;
    background: #00386c;
    color: #fff;
    width: 28px;
    height: 28px;
    display: inline-block;
    position: relative; }

@media (min-width: 992px) and (max-width: 1200px) {
  .ts-breaking-news .breaking-news-content .owl-nav {
    right: -32px;
  }
  .logo img {
    width: 100%;
    padding: 0 5px;
  }
  .nav-menu > li > a {
    padding: 22px 16px;
  }
  /*----------- centerd menu and icon ----*/
  .menu-centerd .ts-main-menu .nav-menu li a {
    padding: 22px 13px;
  }
  .menu-centerd.nav-icon-item .nav-menu li a {
    padding: 22px 41px;
  }
  .header-transparent .nav-menu > li > a {
    padding: 31px 17px;
  }
  #breaking_slider .owl-nav {
    right: -32px;
  }
  .header-box-right .navigation:before {
    display: none;
  }
  .header-box-right .right-menu li .nav-search .nav-search-button {
    text-align: center;
  }
  .footer-top .footer-menu ul li a {
    margin: 0 12px;
  }
  .footer-bottom .footer-social-list li a {
    margin: 0 30px;
  }
  .top-bar.transparent #breaking_slider .owl-nav {
    top: 0;
    right: -19px;
  }
  .footer .footer-social-list li a {
    margin-left: 12px;
  }
  #post-slider1 .owl-nav {
    left: 90px;
  }
  .accordion-post-style .card .card-header a .post-title {
    font-size: 17px;
  }
  .blog-post-slider-item .ts-post-thumb {
    margin-left: -230px;
  }
  .blog-post-slider-item .ts-grid-box {
    padding: 90px 208px 90px 30px;
  }
  .posts-ad img {
    max-width: 100%;
  }
  .footer-social li {
    margin-bottom: 14px;
  }
  .header-box-transprent .ts-main-menu .nav-menu > li > a {
    padding: 24px 13px;
  }
  .ts-tranding-post .slider-indicators .post-content .post-count {
    width: 48px;
    height: 48px;
    padding: 0 12px;
  }
  .footer-social li {
    margin-bottom: 12px;
  }
  .widget-banner {
    text-align: center;
  }
  /*--------- single post ----*/
  .single-post ul li {
    margin-bottom: 14px;
    font-size: 14px;
  }
  #featured-slider-4 .owl-nav {
    bottom: 100px;
  }
  .post-title.ex-lg {
    font-size: 36px;
  }
  .post-title.lg {
    font-size: 30px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .pl-1 {
    padding-left: 15px !important;
  }
  .p-1 {
    padding: 0 15px !important;
  }
  .pr-1,
  .pr-10 {
    padding-right: 15px !important;
  }
  .pr-0 {
    padding-right: 15px !important;
  }
  .p-0 {
    padding: 0 15px !important;
  }
  .pl-0 {
    padding-left: 15px !important;
  }
  .top-social {
    text-align: center;
  }
  .md-center-item {
    text-align: center;
  }
  .posts-ad {
    text-align: center;
  }
  .posts-ad img {
    max-width: 100%;
  }
  .header-standerd .nav-toggle:before,
  .header-box-right .nav-toggle:before {
    background-color: #ffffff;
    -webkit-box-shadow: 0 0.5em 0 0 #ffffff, 0 1em 0 0 #ffffff;
    box-shadow: 0 0.5em 0 0 #ffffff, 0 1em 0 0 #ffffff;
  }
  .header-standerd .nav-menu > li > a {
    color: #232232;
  }
  .nav-icon-item .nav-menu li a i {
    text-align: left !important;
  }
  .ts-breaking-news .breaking-news-content {
    width: 100%;
  }
  .ts-breaking-news .breaking-news-content .owl-nav {
    right: 0;
    top: -27px;
  }
  .logo {
    display: none;
  }
  .mobile-logo {
    display: block;
  }
  .header-nav-item {
    padding-left: 15px;
  }
  .ts-overlay-style.ts-featured .item {
    min-height: 400px;
  }
  .post-title.lg {
    font-size: 30px;
  }
  .ts-overlay-style.ts-featured .item .post-content {
    padding: 17px;
  }
  .ts-featured,
  .posts-ad,
  .ts-overlay-style,
  .ts-grid-box:last-of-type,
  .ts-newslatter-content {
    margin-bottom: 30px;
  }
  .footer-logo {
    margin-bottom: 15px;
  }
  .footer-social li {
    margin-right: 25px;
  }
  #breaking_slider .owl-nav {
    top: -24px;
    right: 0;
  }
  .nav-menu .megamenu-panel {
    -webkit-box-shadow: none;
    box-shadow: none;
    padding: 0 15px;
  }
  .megamenu-panel .item {
    margin-bottom: 15px;
  }
  /*---------- single post style -------------*/
  .post-layout-2 .single-big-img .entry-header {
    padding: 12px 27px;
  }
  /*--- health index----*/
  .ts-grid-item .item {
    margin-bottom: 20px;
  }
  .ts-grid-item .ts-grid-content.ts-list-post-box .item {
    margin-bottom: 0;
  }
  /*------- food index css -----*/
  .nav-icon-item .navigation {
    height: 70px;
  }
  .nav-icon-item .nav-menu li a i {
    text-align: left;
  }
  .nav-icon-item .ts-main-menu .right-menu li .nav-search .nav-search-button {
    text-align: left;
    padding: 25px 45px !important;
  }
  .ts-pagination-1 {
    margin-bottom: 30px;
  }
  /*------- technology index css --------------*/
  .navigation-portrait .submenu-indicator {
    height: auto;
    top: 11px;
  }
  .header-transparent .navigation-portrait .nav-menus-wrapper {
    background: #000032;
  }
  .header-transparent .navigation-portrait .submenu-indicator {
    top: 27px;
  }
  .post-title.ex-lg {
    font-size: 30px;
  }
  .ts-grid-features-box {
    padding: 60px 40px 50px;
  }
  .left-sidebar {
    margin-bottom: 30px;
  }
  .ts-footer.ts-footer-3 .footer-social-list li a {
    margin-left: 25px;
  }
  /*--------------------------
    business index css
    ----------------*/
  .footer-widget {
    margin-bottom: 40px;
  }
  /*--------------------- crypto index css ------------*/
  .header-box-right .navigation:before {
    display: none;
  }
  .header-box-right .nav-menu > li.active {
    background: #232323;
    color: #fff;
  }
  .header-box-right .nav-menu > li > a {
    height: 60px;
  }
  .header-box-right .nav-menu > li > a:before {
    display: none;
  }
  .breakink-news-section #breaking_slider .owl-nav {
    top: -32px;
  }
  .breakink-news-section #breaking_slider .owl-nav .owl-prev,
  .breakink-news-section #breaking_slider .owl-nav .owl-next {
    height: 26px;
  }
  .ts-footer-5 .footer-logo {
    text-align: left;
  }
  .ts-footer-5 .footer-social-list {
    text-align: left !important;
  }
  .ts-footer-5 .footer-widget.newsletter-widgets {
    padding-left: 30px;
  }
  .footer-menu ul li a {
    margin: 0 10px;
  }
  /*----------- index default four -------------*/
  .navbar-standerd .navigation-portrait .nav-brand {
    line-height: 70px;
  }
  .navbar-standerd .nav-menu > li > a {
    height: 60px;
  }
  /*---------------------------- sport index ------------*/
  .header-box-transprent {
    top: 80px;
  }
  .header-box-transprent .ts-main-menu .nav-menu > li > a {
    color: #232323;
  }
  .footer-top .footer-menu.xs-center {
    text-align: center !important;
  }
  .footer-top .footer-logo {
    margin: 20px 0 30px;
  }
  .footer-bottom .footer-social-list {
    margin: 20px 0;
  }
  .footer-bottom .copy-right-text.xs-center {
    text-align: center !important;
  }
  .p-10 {
    padding: 0 15px;
  }
  /*----------------- travel index css ------------*/
  .header-transprent {
    top: 25px;
  }
  .header-transprent .nav-menu > li > a:hover {
    color: #232232;
  }
  .header-transprent .nav-menu > li > a .submenu-indicator {
    margin-top: 14px;
  }
  .header-transprent .nav-menu > li:first-child > a {
    padding-left: 15px;
  }
  .header-transprent .nav-toggle:before {
    background-color: #fff;
    border-radius: 10px;
    -webkit-box-shadow: 0 0.5em 0 0 #fff, 0 1em 0 0 #fff;
    box-shadow: 0 0.5em 0 0 #fff, 0 1em 0 0 #fff;
  }
  .header-transprent .mobile_hidden {
    display: none;
  }
  .header-transprent .nav-menu-centered .header-search {
    display: none;
  }
  .header-box-transprent .ts-main-menu .nav-menu > li:first-child a {
    padding-left: 20px;
  }
  .featured-area .item {
    height: 600px;
  }
  #post-slider1 .owl-nav {
    left: 0;
  }
  .post-overflow-style .post-content-item {
    padding: 70px 20px 20px 40px;
    margin-left: 0;
  }
  .block-wrapper-1.p-50 {
    padding: 30px 0;
    margin: 0;
  }
  .grid-style-2 .ts-overlay-style .item {
    min-height: 350px;
  }
  .accordion-post-style {
    margin-bottom: 30px;
  }
  .col-lg-3.pl-0 .accordion-post-style {
    margin-bottom: 0px;
  }
  .watching-section .item {
    margin-bottom: 20px;
  }
  .footer .copyright-text {
    margin-bottom: 20px;
    text-align: center;
  }
  section.block-wrapper.block-wrapper-1.p-50.mb-40,
  section.block-wrapper.block-wrapper-1.p-50.mt-50 {
    padding-bottom: 0;
  }
  section.block-wrapper.block-wrapper-1.p-100 {
    padding: 30px 0;
  }
  .ts-overlay-style .overlay-post-content {
    width: 100%;
  }
  .blog-post-slider-item .ts-post-thumb {
    margin-left: -230px;
  }
  .blog-post-slider-item .ts-grid-box {
    padding: 90px 208px 90px 30px;
  }
  .post-col-list-item .older-post-btn {
    margin-bottom: 40px;
  }
  .navbar-standerd.nav-bar-dark .navigation-portrait .nav-menus-wrapper {
    background: #232323;
  }
  .navbar-standerd.nav-bar-dark .navigation-portrait .nav-menu > li {
    border-top: solid 1px #444444;
  }
  .category-box-item-3 .row .col-lg-6 .item {
    margin-bottom: 20px;
  }
  .category-item .item {
    margin-bottom: 20px;
  }
  section.block-wrapper.mb-45 {
    margin-bottom: 30px;
  }
  .post-list-box.ts-list-post-box.ts-grid-content {
    margin-bottom: 20px;
  }
  .row.post-col-list-item .col-lg-6.mb-30 {
    margin-bottom: 0 !important;
  }
  #more-news-section {
    margin-bottom: 0 !important;
  }
  #featured-slider-4 .item {
    min-height: 350px;
  }
  .plr-30 {
    padding: 0 15px;
  }
  .md-center {
    text-align: center !important;
  }
  .ts-grid-item.ts-grid-item1 .col-lg-6 .ts-post-thumb img {
    max-height: inherit;
    width: 100%;
  }
  .widget-banner,
  .banner-widget {
    text-align: center;
  }
  .ts-post-overlay-style-1 .ts-overlay-style:last-of-type {
    margin-bottom: 0;
  }
  .ts-post-style-5.ts-post-overlay-style-1 .ts-overlay-style:last-of-type {
    margin-bottom: 30px;
  }
  .post-list-box.ts-list-post-box.ts-grid-content .ts-overlay-style {
    margin-bottom: 10px;
  }
  .md-mb-30,
  .ts-pagination.text-center.mb-20 {
    margin-bottom: 30px;
  }
  /*--- sidebar ----*/
  .widget-banner {
    text-align: center;
  }
  .right-sidebar-1 .widgets .category-list {
    margin-bottom: 20px;
  }
  .sidebar {
    margin-top: 20px;
  }
  .single-post.content-wrapper, .comments-form.ts-grid-box {
    padding: 30px 40px 40px;
  }
  .single-post .post-media {
    margin: 0 -40px;
  }
  .post-meta-info li {
    margin-right: 20px;
    margin-bottom: 12px;
  }
  .category-layout-3 .ts-grid-box.entry-header {
    margin-bottom: 0;
  }
  .category-layout-3 .col-lg-6.mb-30 {
    margin-bottom: 0 !important;
  }
  .ts-tranding-post .slider-indicators.carousel-indicators > li:before,
  .ts-tranding-post .slider-indicators.carousel-indicators > li:after {
    display: none;
  }
  .category-box-item-3 .col-lg-6 .item {
    margin-bottom: 30px;
  }
  .ts-grid-box.category-box-item-3,
  .ts-grid-box.category-item {
    padding-bottom: 20px;
  }
  #more-news-section {
    padding-top: 0;
  }
  /*--------- single post ----*/
  .single-post ul li {
    margin-bottom: 14px;
    font-size: 14px;
  }
  .ts-footer-5 .footer-widget.border-right:after {
    display: none;
  }
}

@media (max-width: 767px) {
  .pl-1 {
    padding-left: 15px !important;
  }
  .p-1 {
    padding: 0 15px !important;
  }
  .pr-1 {
    padding-right: 15px !important;
  }
  .pr-0 {
    padding-right: 15px !important;
  }
  .p-0 {
    padding: 0 15px !important;
  }
  .pl-0 {
    padding-left: 15px !important;
  }
  .xs-center {
    text-align: center !important;
  }
  /*--- top bar ---*/
  .top-bar .ts-temperature {
    text-align: center;
    border-right: none;
    display: block;
    padding-right: 0;
  }
  .top-bar .ts-temperature:before {
    display: none;
  }
  .top-bar .top-social {
    text-align: center;
  }
  .top-bar .ts-top-nav {
    display: block;
    text-align: center;
    padding-left: 0;
  }
  .top-bar.v3 .ts-date {
    display: block;
    padding-right: 0;
    text-align: center;
  }
  .logo {
    display: none;
  }
  .mobile-logo {
    display: block;
  }
  .header-nav-item {
    padding-left: 15px;
  }
  #breaking_slider {
    width: 100%;
  }
  #breaking_slider .owl-nav {
    position: absolute;
    right: 0;
    top: -27px;
  }
  .header-middle.v2 .logo {
    display: block;
  }
  /*---- blog post ----*/
  .ts-overlay-style.ts-featured .item {
    min-height: 440px;
  }
  .post-title.lg {
    font-size: 30px;
  }
  .ts-overlay-style.ts-featured .item .post-content {
    padding: 17px;
  }
  .ts-featured,
  .posts-ad,
  .ts-overlay-style,
  .ts-grid-box:last-of-type,
  .ts-newslatter-content {
    margin-bottom: 30px;
  }
  .watch-now-featured .item .ts-video-btn {
    top: 40px;
  }
  .ts-tranding-post .slider-indicators.carousel-indicators > li {
    width: 100%;
  }
  #more-news-slider .ts-overlay-style {
    margin-bottom: 0;
  }
  .ts-grid-box {
    padding: 15px;
  }
  .ts-title:before {
    left: -15px;
  }
  .ts-tranding-post .ts-title {
    margin-left: 0px;
  }
  .ts-tranding-post .carousel-inner .ts-post-thumb img {
    min-height: 260px;
  }
  /*- footer--*/
  .footer-social li {
    margin-right: 20px;
    margin-top: 15px;
  }
  .footer-logo {
    margin-bottom: 20px;
  }
  .newsletter-form .media {
    display: block;
  }
  .ts-newslatter .newsletter-form .ts-submit-btn {
    margin-left: 38px;
    margin-top: 20px;
  }
  .ts-footer .footer-menu ul li a {
    margin: 0 8px;
  }
  /*-- single post ---*/
  .single-post.ts-grid-box, .comments-form.ts-grid-box {
    padding: 30px 15px 30px;
  }
  .post-meta-info li {
    margin-right: 16px;
    margin-bottom: 10px;
  }
  .single-post .post-media {
    margin: 0;
  }
  blockquote {
    font-size: 17px;
    line-height: 35px;
    padding: 0 0 0 18px;
    margin: 50px 0;
  }
  blockquote cite {
    display: none;
  }
  p img.float-left {
    margin-right: 0;
    width: 100%;
  }
  .author-box {
    padding: 20px 0 20px 70px;
  }
  .author-box .author-img {
    width: 60px;
    height: 60px;
  }
  .author-box .authors-social {
    float: none;
    display: block;
    width: 100%;
    clear: both;
  }
  .author-box .authors-social a {
    margin-left: 16px;
  }
  .post-navigation .post-previous, .post-navigation .post-next {
    width: 100%;
  }
  .post-navigation .post-previous {
    margin-bottom: 20px;
    border-right: none;
  }
  /*---------- single post style -------------*/
  .post-layout-2 .single-big-img .entry-header {
    padding: 12px 27px;
  }
  .breadcrumb li {
    padding: 7px 26px 7px 17px;
  }
  /*------------------ category --------*/
  .ts-category-list {
    margin-bottom: 30px;
    margin-top: 10px;
  }
  .ts-category-list li a {
    padding: 2px 9px;
  }
  /*--- health  index css----*/
  .top-bar.top-bg .ts-date {
    text-align: center;
  }
  .top-bar.top-bg .top-nav {
    text-align: center;
  }
  .top-bar.top-bg .top-nav li a {
    margin-left: 20px;
  }
  .ts-post-style-2 .ts-overlay-style .ts-post-thumb img {
    min-height: 250px;
  }
  .ts-grid-item .item {
    margin-bottom: 20px;
  }
  .ts-grid-item .ts-grid-content.ts-list-post-box .item {
    margin-bottom: 0;
  }
  /*------- food index css -----*/
  .nav-icon-item .navigation {
    height: 70px;
  }
  .nav-icon-item .nav-menu li a i {
    text-align: left !important;
  }
  .nav-icon-item .ts-main-menu .right-menu li .nav-search .nav-search-button {
    text-align: left;
    padding: 25px 45px !important;
  }
  #featured-slider-3 {
    margin-bottom: 0;
  }
  .featured-content-box .post-content {
    padding: 15px;
    margin: -40px 15px 0px;
  }
  .item.post-content-box {
    margin-bottom: 15px;
  }
  .ts-pagination-1 {
    margin-bottom: 30px;
  }
  .ts-footer-2 .copyright-text {
    text-align: center;
    margin-bottom: 15px;
  }
  .footer-social-list.text-right {
    text-align: center !important;
  }
  /*-------------- technology index css --------*/
  .navigation-portrait .submenu-indicator {
    height: auto;
    top: 11px;
  }
  .header-transparent .navigation-portrait .nav-menus-wrapper {
    background: #000032;
  }
  .header-transparent .navigation-portrait .submenu-indicator {
    top: 27px;
  }
  .post-title.ex-lg {
    font-size: 30px;
  }
  #featured-slider-4 .ts-post-thumb img {
    min-height: inherit;
  }
  .ts-grid-features-box {
    padding: 45px 29px 23px;
  }
  .left-sidebar {
    margin-bottom: 30px;
  }
  .ts-footer.ts-footer-3 .footer-logo {
    padding: 34px 0 15px;
  }
  .ts-footer.ts-footer-3 .footer-menu li a:after {
    display: none;
  }
  .ts-footer.ts-footer-3 .footer-social-list li a {
    margin-left: 30px;
  }
  .ts-footer.ts-footer-3 .copyright-text {
    text-align: center !important;
  }
  /*----------------- business index css-------------*/
  .currency-list-item {
    margin-top: 20px;
  }
  .footer-widget {
    margin-bottom: 40px;
  }
  .ts-footer [class*="col-"]:last-of-type .footer-widget {
    margin-bottom: 0;
  }
  .ts-footer-4 .footer-logo {
    text-align: center;
  }
  .ts-footer-4 .footer-social-list {
    text-align: center !important;
  }
  .copyright-section .footer-menu,
  .copyright-section .copyright-text {
    text-align: center !important;
    margin: 10px 0;
  }
  /*--------------------- crypto index css ------------*/
  .header-box-right .navigation {
    height: 65px;
  }
  .header-box-right .header-logo {
    text-align: center;
    padding: 10px 0;
  }
  .header-box-right .ts-date {
    text-align: center;
  }
  .header-box-right .navigation:before {
    display: none;
  }
  .header-box-right .ts-top-nav {
    text-align: center !important;
  }
  .header-box-right .nav-menu > li.active {
    background: #232323;
    color: #fff;
  }
  .header-box-right .nav-menu > li > a {
    height: 60px;
  }
  .header-box-right .nav-menu > li > a:before {
    display: none;
  }
  .header-box-right .nav-toggle:before {
    background-color: #fff;
    border-radius: 10px;
    -webkit-box-shadow: 0 0.5em 0 0 #fff, 0 1em 0 0 #fff;
    box-shadow: 0 0.5em 0 0 #fff, 0 1em 0 0 #fff;
  }
  .breakink-news-section #breaking_slider .owl-nav {
    top: -32px;
  }
  .breakink-news-section #breaking_slider .owl-nav .owl-prev,
  .breakink-news-section #breaking_slider .owl-nav .owl-next {
    height: 26px;
  }
  .ts-footer-5 .footer-logo {
    text-align: left;
  }
  .ts-footer-5 .footer-social-list {
    text-align: left !important;
  }
  .ts-footer-5 .footer-widget.newsletter-widgets {
    padding-left: 0;
  }
  .category-post-style1 .item {
    margin-bottom: 20px;
  }
  /*------------- home default three ------*/
  .header-middle .header-logo {
    margin-bottom: 15px;
  }
  .header-standerd .nav-menu > li > a {
    color: #232323;
  }
  .header-standerd .nav-toggle:before {
    background-color: #ffffff;
    -webkit-box-shadow: 0 0.5em 0 0 #ffffff, 0 1em 0 0 #ffffff;
    box-shadow: 0 0.5em 0 0 #ffffff, 0 1em 0 0 #ffffff;
  }
  .header-standerd .nav-menu > li.active > a {
    color: #fff;
  }
  .xs-left {
    text-align: left !important;
  }
  /*----------- index default four -------------*/
  .navbar-standerd .navigation-portrait .nav-brand {
    line-height: 70px;
  }
  .navbar-standerd .nav-menu > li > a {
    height: 60px;
  }
  .navbar-standerd.nav-item .nav-menu > li > a {
    line-height: 35px;
  }
  .ts-post-style-2 .ts-overlay-style .item {
    min-height: 360px;
  }
  .header-middle.v4 {
    padding: 13px 0;
  }
  .megamenu-panel .item {
    margin-bottom: 15px;
  }
  .error-page {
    padding: 50px 15px;
  }
  .error-page .error-code h2 {
    font-size: 130px;
    line-height: 130px;
  }
  .error-page .error-message h3 {
    font-size: 25px;
  }
  .error-page .error-body h4 {
    font-size: 14px;
  }
  .nav-menu .megamenu-panel {
    -webkit-box-shadow: none;
    box-shadow: none;
    padding: 0 15px;
  }
  .ad-widget,
  .posts-ad {
    text-align: center;
  }
  .ts-grid-box.most-populer-item,
  .ts-post-overlay-style-1 .ts-overlay-style:last-of-type {
    margin-bottom: 0;
  }
  #more-news-section {
    padding-top: 0;
  }
  .ts-breaking-news .breaking-news-content {
    width: 100%;
  }
  .category-box-item-3 .item,
  .category-item .item {
    margin-bottom: 20px;
  }
  .video-slider .post-video img {
    min-height: 360px;
  }
  .video-slider .post-video .post-video-content {
    padding: 15px;
  }
  .ts-grid-box.ts-category-title {
    padding: 18px 15px;
  }
  .category-list-item .widget-title:before {
    left: -15px;
  }
  .mr--20 {
    margin-right: 0;
  }
  .ts-pagination.text-center.mt-15 {
    margin-bottom: 30px;
  }
  .nav-menu-item.nav-icon-item .navigation {
    height: 70px;
  }
  /*-------------------- sport index -----------*/
  .top-bar.transparent #breaking_slider .owl-nav {
    top: -28px;
    right: 0;
  }
  .header-box-transprent {
    top: 100px;
  }
  .header-box-transprent .ts-main-menu .nav-menu > li > a {
    color: #232323;
  }
  .hero-content h2 {
    font-size: 30px;
  }
  .slider-dot-item {
    left: 0;
    padding-left: 84px;
  }
  .tab-menu-item {
    margin-bottom: 20px;
  }
  .tab-menu-item li a {
    padding: 0 8px;
  }
  .video-item {
    height: 300px;
  }
  .footer-top .footer-menu ul li a {
    margin: 0 13px;
  }
  .footer-top .footer-logo {
    margin-top: 20px;
    margin-bottom: 30px;
  }
  .footer-bottom .footer-social-list {
    margin: 20px 0;
  }
  .footer-bottom .footer-social-list li a {
    margin: 0 17px;
  }
  .p-10 {
    padding: 0 15px;
  }
  /*----------------- travel index css ------------*/
  .header-transprent {
    top: 25px;
  }
  .header-transprent .nav-menu > li > a:hover {
    color: #232232;
  }
  .header-transprent .nav-menu > li > a .submenu-indicator {
    margin-top: 14px;
  }
  .header-transprent .nav-menu > li:first-child > a {
    padding-left: 15px;
  }
  .header-transprent .nav-toggle:before {
    background-color: #fff;
    border-radius: 10px;
    -webkit-box-shadow: 0 0.5em 0 0 #fff, 0 1em 0 0 #fff;
    box-shadow: 0 0.5em 0 0 #fff, 0 1em 0 0 #fff;
  }
  .header-transprent .mobile_hidden {
    display: none;
  }
  .header-transprent .nav-menu-centered .header-search {
    display: none;
  }
  .header-box-transprent .ts-main-menu .nav-menu > li:first-child a {
    padding-left: 20px;
  }
  .featured-area .item {
    height: 600px;
  }
  .featured-area .item .hero-content h2 {
    font-size: 30px;
  }
  .featured-area .item .hero-content .featurd-video-icon {
    font-size: 58px;
  }
  #post-slider1 .owl-nav {
    left: 0;
  }
  .post-overflow-style .post-content-item {
    padding: 70px 20px 20px 40px;
    margin-left: 0;
  }
  .block-wrapper-1.p-50 {
    padding: 30px 0;
    margin: 0;
  }
  .grid-style-2 .ts-overlay-style .item {
    min-height: 350px;
  }
  .accordion-post-style {
    margin-bottom: 30px;
  }
  .col-lg-3.pl-0 .accordion-post-style {
    margin-bottom: 0px;
  }
  .watching-section .item {
    margin-bottom: 20px;
  }
  .footer .copyright-text {
    margin-bottom: 20px;
    text-align: center;
  }
  section.block-wrapper.block-wrapper-1.p-50.mb-40,
  section.block-wrapper.block-wrapper-1.p-50.mt-50 {
    padding-bottom: 0;
  }
  section.block-wrapper.block-wrapper-1.p-100 {
    padding: 30px 0;
  }
  .navbar-standerd .top-social {
    padding-left: 10px;
  }
  .navbar-standerd .top-social li {
    margin-right: 0;
  }
  .block-wrapper.p-60 {
    padding: 40px 0;
  }
  .blog-post-slider-item .ts-grid-box {
    padding: 34px 20px 50px 30px;
    margin-bottom: 0;
  }
  .blog-post-slider-item .ts-post-thumb {
    margin: 0;
  }
  .blog-post-slider-item.mb-60 {
    margin-bottom: 30px;
  }
  .post-col-list-item .older-post-btn {
    margin-bottom: 40px;
  }
  .post-col-list-item [class*="col-"]:last-of-type {
    margin-bottom: 0;
  }
  .instagramPhoto .col-md.p-0 {
    margin-right: 0;
  }
  .category-widget .widget-title:before {
    left: -15px;
  }
  .ts-grid-box.widgets.tag-list .widget-title:before {
    left: -15px;
  }
  .logo-area {
    padding: 30px 15px;
  }
  .post-layout-7 .single-big-img .entry-header-item {
    padding-top: 245px;
  }
  .social-widget .widget-title:before {
    left: -15px;
  }
  .entry-cat-header .ts-title {
    margin-bottom: 20px;
  }
  .category-layout-3 .col-lg-9 .row .col-lg-6.mb-30 {
    margin-bottom: 0 !important;
  }
  .post-col-list-item .col-lg-6.mb-30 {
    margin-bottom: 0 !important;
  }
  .ts-post-style-3 .col-lg-4.p-1:last-of-type .ts-overlay-style {
    margin-bottom: 0;
  }
  .ts-col-list-post .col-lg-4.col-md-6.mb-30 {
    margin-bottom: 0 !important;
  }
  .navbar-standerd.nav-bar-dark .navigation-portrait .nav-menus-wrapper {
    background: #232323;
  }
  .navbar-standerd.nav-bar-dark .navigation-portrait .nav-menu > li {
    border-top: solid 1px #444444;
  }
  .widget-banner {
    text-align: center;
  }
  .uk-width-3-4 {
    width: 100%;
  }
  .col-lg-3.pr-10 .ts-post-overlay-style-1 {
    margin-bottom: 30px;
  }
  .post-list-box.ts-list-post-box.ts-grid-content {
    margin-bottom: 20px;
  }
  .post-list-box.ts-list-post-box.ts-grid-content .ts-overlay-style {
    margin-bottom: 10px;
  }
  #featured-slider-5 {
    margin-bottom: 0;
  }
  .block-wrapper.ts-post-style-4 {
    padding-bottom: 0;
  }
  #most-pupuler {
    margin-bottom: 30px;
  }
  .ts-overlay-item.ts-grid-style-2 [class*='col-']:last-of-type .ts-overlay-style {
    margin-bottom: 0;
  }
  .ts-grid-item.ts-overlay-item [class*='col-']:last-of-type .ts-overlay-style {
    margin-bottom: 0;
  }
  .featured-tab-item .featured-tab-post > li {
    border-right: none;
    border-bottom: 1px solid #ddd;
  }
  .category-layout-1 [class*='col-']:last-of-type .ts-overlay-style {
    margin-bottom: 0;
  }
  .category-layout-5 .ts-grid-box.entry-header {
    margin-bottom: 0;
  }
  .featured-post.post-style-4 .ts-overlay-style {
    margin-bottom: 30px;
  }
  .featured-post.post-style-4 [class*='col-']:last-of-type .ts-overlay-style {
    margin-bottom: 0px;
  }
  .ts-content-box-item .row.mb-30 {
    margin-bottom: 0 !important;
  }
  .ts-pagination.text-center.mb-20,
  .xs-mb-30,
  .ts-grid-style-3 .ts-overlay-style {
    margin-bottom: 30px;
  }
  .plr-10 {
    padding: 0 15px;
  }
  .ts-grid-style-3 .ts-overlay-style:last-of-type {
    margin-bottom: 0;
  }
  .ts-tranding-post .slider-indicators.carousel-indicators > li:before,
  .ts-tranding-post .slider-indicators.carousel-indicators > li:after {
    display: none;
  }
  .widget-banner {
    text-align: center;
  }
  .ts-footer-5 .footer-widget.border-right:after {
    display: none;
  }
  #featured-slider-4 .item {
    min-height: 365px;
  }
}

{% load wagtailcore_tags %}

<div class="rich-text-block table-container">
    <div class="table-responsive">
        {% if value.table_caption %}
            <div class="table-caption">{{ value.table_caption }}</div>
        {% endif %}
        
        <table class="table">
            {% if value.first_row_is_table_header and value.data %}
            <thead>
                <tr>
                    {% for cell in value.data.0 %}
                        <th>{{ cell }}</th>
                    {% endfor %}
                </tr>
            </thead>
            <tbody>
                {% for row in value.data|slice:"1:" %}
                <tr>
                    {% for cell in row %}
                        <td>{{ cell }}</td>
                    {% endfor %}
                </tr>
                {% endfor %}
            </tbody>
            {% else %}
            <tbody>
                {% for row in value.data %}
                <tr>
                    {% for cell in row %}
                        <td>{{ cell }}</td>
                    {% endfor %}
                </tr>
                {% endfor %}
            </tbody>
            {% endif %}
        </table>
    </div>
</div> 
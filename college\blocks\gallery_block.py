from django.core.exceptions import ValidationError
from wagtail import blocks

from college.blocks.image_block import ImageBlock


class GalleryBlock(blocks.StructBlock):
    title = blocks.CharBlock(required=False)
    images = blocks.ListBlock(ImageBlock(), help_text="Add at least 5 images for gallery stories")
    
    def clean(self, value):
        result = super().clean(value)
        
        # Check if this is a gallery with minimum 5 images
        if len(result['images']) < 5:
            raise ValidationError(
                "Gallery stories must contain at least 5 images. "
                f"You have only added {len(result['images'])} images."
            )
        return result
    
    class Meta:
        icon = 'grip'
        template = 'blocks/gallery_block.html'
        label = 'Gallery'
        help_text = "Gallery stories must contain at least 5 images"
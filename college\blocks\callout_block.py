from wagtail import blocks


class CalloutBlock(blocks.StructBlock):
    heading = blocks.CharBlock(required=False)
    text = blocks.RichTextBlock(required=True)
    style = blocks.ChoiceBlock(
        choices=[
            ('info', 'Info'),
            ('warning', 'Warning'),
            ('success', 'Success'),
            ('danger', 'Danger'),
        ],
        default='info',
    )
    
    class Meta:
        icon = 'warning'
        template = 'blocks/callout_block.html'
        label = 'Callout'
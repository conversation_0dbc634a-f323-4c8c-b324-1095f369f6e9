@font-face {
  font-family: 'icomoon';
  src:  url('../fonts/icofonts/icomoon.eot?ksklqv');
  src:  url('../fonts/icofonts/icomoon.eot?ksklqv#iefix') format('embedded-opentype'),
    url('../fonts/icofonts/icomoon.ttf?ksklqv') format('truetype'),
    url('../fonts/icofonts/icomoon.woff?ksklqv') format('woff'),
    url('../fonts/icofonts/icomoon.svg?ksklqv#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-add:before {
  content: "\e900";
}
.icon-arrow-left:before {
  content: "\e901";
}
.icon-arrow-right:before {
  content: "\e902";
}
.icon-desert:before {
  content: "\e903";
}
.icon-drinks:before {
  content: "\e904";
}
.icon-fastfood:before {
  content: "\e905";
}
.icon-fire:before {
  content: "\e906";
}
.icon-home:before {
  content: "\e907";
}
.icon-kids:before {
  content: "\e908";
}
.icon-paly-button-1:before {
  content: "\e909";
}
.icon-paly-button-2:before {
  content: "\e90a";
}
.icon-recepies:before {
  content: "\e90b";
}
.icon-search:before {
  content: "\e90c";
}
.icon-weather:before {
  content: "\e90d";
}

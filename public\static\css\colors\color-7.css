/* home technology*/
/* primary color set*/

.breaking-title,
.post-title a:hover,
.copyright-text a,
.nav-menu .megamenu-lists .megamenu-list > li > a:hover,
.ts-grid-features-box .post-content .post-title a:hover,
.ts-grid-features-box .post-meta-info li.active,
.ts-heading .ts-title:before,
.post-tag a,
.ts-footer.ts-footer-3 .footer-menu li a:hover,
.ts-footer.ts-footer-3 .footer-social-list li a:hover,
.overlay-heighlight-orange .post-meta-info li.active,
.header-transparent .nav-menu > li.active > a,
.header-transparent .nav-menu > li > a:hover,
.nav-menu > li .nav-dropdown li a:hover{
    color: #007dff !important;
}
/* primary background0 color set*/
.ts-title-item .ts-title:before,
.widget-title:before,
.pagination li.active a,
.pagination li a:hover,
 .post-cat,
.widgets .ts-widget-newsletter,
.widgets.single-widget .widget-title:before,
.ts-heading .ts-title:before,
.header-transparent .nav-menu > li > a:before{
    background: #007dff;
}
/* overlay post title hover color*/
.overlay-post-content .post-title a:hover,
.ts-post-thumb .post-cat{
    color: #fff!important;
}
/* newsletter btn color*/
.widgets .ts-widget-newsletter .newsletter-form .btn{
    background: #1067c4;
}
/* post content color*/
.post-content p{
    font-size: 15px;
    color: #232323;
  }
  
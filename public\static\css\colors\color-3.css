/*--- default home 4 --*/
/*--- default primary color set --*/

.navbar-standerd .nav-menu > li.active > a,
.navbar-standerd.nav-item .nav-menu > li > a:before,
.navbar-standerd .nav-menu > li > a:hover,
.ts-top-nav li a:hover,
.navbar-standerd .nav-menu > li > a:hover,
.ts-heading .view-all-link:hover,
.ts-grid-item .ts-overlay-style .post-meta-info li.active,
.post-title a:hover,
.nav-menu .megamenu-tabs-nav > li.active a,
.nav-menu > li .nav-dropdown li a:hover,
.breaking-title,
.post-list-box .post-content .post-title:hover,
.widgets ul li a:hover{
    color: #e21e22 !important;
}
/*--- default primary background color set --*/
.ts-heading .ts-title:before,
.widget-title:before,
.widgets .ts-widget-newsletter,
.ts-newslatter .newsletter-form .ts-submit-btn .btn:hover,
.top-bar .top-social li.ts-date,
.navbar-standerd.nav-item .nav-menu > li > a:before,
.pagination li.active a, .pagination li:hover a,
.top-bar .ts-date-item{
    background: #e21e22;
}
/*-- overlay post title hover color ---*/
.overlay-post-content .post-title a:hover{
    color: #fff !important;
}
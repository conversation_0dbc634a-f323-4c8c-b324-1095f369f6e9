/* home travel*/
/* post cat and submit btn color */
.post-cat,
.accordion-post-style .card .btn,
.footer .ts-submit-btn{
    background: #ee4201;
}
.post-title a:hover,
.footer-social-list li a i:hover{
    color: #ee4201;
}
/*section bd color*/
.section-bg{
    background: #373ba9;
}

/*--------------- body content css ----*/

.ts-title-item .ts-title{
    margin-left: 0;
    color: #772f79;
    font-size: 24px;
}
.ts-title-item .ts-title:before{
    display: none;
}
.post-cat{
    margin-left: 0;
    border-radius: 36px;
    height: 22px;
    margin-bottom: 10px;
}
.ts-overlay-style .item:before{
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 55%, rgba(0, 0, 0, 0.45) 100%);
}
.footer{
    padding: 60px 0 50px;
}
.footer .newsletter-mail{
    height: 45px;
    border-radius: 36px;
    padding: 0 20px;
    font-size: 14px;
}
.footer .ts-submit-btn{
    position: absolute;
    right: 3px;
    top: 0;
    border-radius: 36px;
    padding: 10px 24px;
    bottom: 0;
    height: 40px;
    margin: auto;
    font-size: 12px;
}
.footer .newsletter-form {
    margin: 0 30px;
}
.footer .newsletter-forms{
    position: relative;
}
.footer-social-list li a i,
.footer-social-list li{
    color: #fff;
}
/* slider nav*/
#post-slider1{
    margin-top: -55px;
}
#post-slider1 .owl-stage-outer{
    padding-top: 55px;
}
#post-slider1 .owl-nav {
    position: relative;
    right: 0;
    top: -65px;
    left: 65px;
    margin: auto;
    text-align: center;
    padding-left: 55px;
}
/* slider nav*/
#post-slider1 .owl-nav .owl-prev:before {
    display: none;
}
#post-slider1 .owl-nav .owl-next i, .owl-nav .owl-prev i{
    font-size: 12px;
}